// 内容解析和学习进度跟踪工具函数

export interface ChapterSection {
  id: string
  title: string
  estimatedTime: number
  type: 'intro' | 'chapter' | 'checkpoint' | 'interactive' | 'conclusion'
  completed: boolean
  currentlyViewing: boolean
  subsections?: ChapterSection[]
  element?: Element
}

export interface LearningProgress {
  tutorialId: number
  completedSections: string[]
  currentSection: string
  totalTimeSpent: number
  progressPercentage: number
  lastAccessed: string
  sectionTimeSpent: Record<string, number>
  
  // 新增字段支持混合进度计算
  scrollProgress?: number // 滚动进度 0-100
  timeInView?: Record<string, number> // 每个章节的可视时间(秒)
  interactionEvents?: number // 交互事件数量
  readingSpeed?: number // 阅读速度 (字符/分钟)
}

/**
 * 从HTML内容中解析章节结构
 * 支持结构化教程和普通HTML教程
 */
export function parseContentSections(htmlContent: string): ChapterSection[] {
  if (typeof window === 'undefined') {
    return []
  }

  const parser = new DOMParser()
  const doc = parser.parseFromString(htmlContent, 'text/html')
  
  // 查找所有带有 data-section 属性的元素
  const sectionElements = doc.querySelectorAll('[data-section]')
  const sections: ChapterSection[] = []
  
  sectionElements.forEach((element, index) => {
    const sectionId = element.getAttribute('data-section') || `section-${index}`
    const sectionTitle = element.getAttribute('data-section-title') || 
                        element.querySelector('h1, h2, h3')?.textContent || 
                        `第${index + 1}部分`
    const estimatedTime = parseInt(element.getAttribute('data-estimated-time') || '5')
    
    // 根据内容和类名判断类型
    let type: ChapterSection['type'] = 'chapter'
    if (sectionId === 'intro' || element.tagName.toLowerCase() === 'header') {
      type = 'intro'
    } else if (sectionId === 'conclusion' || sectionTitle.includes('总结')) {
      type = 'conclusion'
    } else if (element.querySelector('[data-checkpoint]')) {
      type = 'checkpoint'
    } else if (element.querySelector('[data-interactive]')) {
      type = 'interactive'
    }
    
    // 查找子章节（如果有）
    const subsectionElements = element.querySelectorAll('[data-subsection]')
    const subsections: ChapterSection[] = []
    
    subsectionElements.forEach((subElement, subIndex) => {
      const subId = subElement.getAttribute('data-subsection') || `${sectionId}-${subIndex}`
      const subTitle = subElement.getAttribute('data-subsection-title') ||
                      subElement.querySelector('h3, h4')?.textContent ||
                      `子章节 ${subIndex + 1}`
      const subTime = parseInt(subElement.getAttribute('data-estimated-time') || '3')
      
      subsections.push({
        id: subId,
        title: subTitle,
        estimatedTime: subTime,
        type: 'chapter',
        completed: false,
        currentlyViewing: false,
        element: subElement
      })
    })
    
    sections.push({
      id: sectionId,
      title: sectionTitle,
      estimatedTime: estimatedTime,
      type: type,
      completed: false,
      currentlyViewing: false,
      subsections: subsections.length > 0 ? subsections : undefined,
      element: element
    })
  })
  
  // 如果没有找到结构化的章节，智能解析普通HTML
  if (sections.length === 0) {
    sections.push(...parseRegularHTML(doc))
  }
  
  return sections
}

/**
 * 解析普通HTML内容，自动生成章节结构
 */
function parseRegularHTML(doc: Document): ChapterSection[] {
  const sections: ChapterSection[] = []
  const headings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')
  
  // 如果没有标题，创建一个默认章节
  if (headings.length === 0) {
    return [{
      id: 'content',
      title: '教程内容',
      estimatedTime: 10,
      type: 'chapter',
      completed: false,
      currentlyViewing: false,
      element: doc.body
    }]
  }
  
  headings.forEach((heading, index) => {
    const text = heading.textContent?.trim() || ''
    if (text) {
      // 估算阅读时间（基于标题后的内容）
      const estimatedTime = estimateReadingTime(heading)
      
      // 判断章节类型
      let type: ChapterSection['type'] = 'chapter'
      if (index === 0 || text.includes('介绍') || text.includes('概述')) {
        type = 'intro'
      } else if (text.includes('总结') || text.includes('结论') || text.includes('小结')) {
        type = 'conclusion'
      } else if (text.includes('练习') || text.includes('实践') || text.includes('作业')) {
        type = 'interactive'
      } else if (text.includes('检查') || text.includes('测试') || text.includes('验证')) {
        type = 'checkpoint'
      }
      
      // 为标题元素添加ID（用于导航）
      if (!heading.id) {
        heading.id = `heading-${index}`
      }
      
      sections.push({
        id: heading.id || `heading-${index}`,
        title: text,
        estimatedTime: estimatedTime,
        type: type,
        completed: false,
        currentlyViewing: false,
        element: heading
      })
    }
  })
  
  return sections
}

/**
 * 估算标题对应内容的阅读时间
 */
function estimateReadingTime(heading: Element): number {
  const nextElements = getContentAfterHeading(heading)
  const wordCount = nextElements.reduce((count, el) => {
    const text = el.textContent || ''
    // 中文按字符数，英文按单词数
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length
    return count + chineseChars + englishWords
  }, 0)
  
  // 阅读速度：中文200字/分钟，英文250词/分钟
  const minutes = Math.max(2, Math.ceil(wordCount / 200))
  return Math.min(minutes, 30) // 限制在2-30分钟
}

/**
 * 获取标题后的内容元素（直到下一个同级或更高级标题）
 */
function getContentAfterHeading(heading: Element): Element[] {
  const elements: Element[] = []
  const headingLevel = parseInt(heading.tagName[1])
  let currentElement = heading.nextElementSibling
  
  while (currentElement) {
    // 如果遇到同级或更高级标题，停止
    if (currentElement.tagName.match(/^H[1-6]$/)) {
      const currentLevel = parseInt(currentElement.tagName[1])
      if (currentLevel <= headingLevel) {
        break
      }
    }
    
    elements.push(currentElement)
    currentElement = currentElement.nextElementSibling
  }
  
  return elements
}

/**
 * 计算学习进度百分比 - 增强版
 * 支持基于滚动、时间和手动标记的混合计算
 */
export function calculateProgressPercentage(
  sections: ChapterSection[],
  completedSections: string[]
): number {
  if (sections.length === 0) return 0
  
  const totalSections = sections.reduce((count, section) => {
    return count + 1 + (section.subsections?.length || 0)
  }, 0)
  
  const completed = sections.reduce((count, section) => {
    let sectionCount = completedSections.includes(section.id) ? 1 : 0
    if (section.subsections) {
      sectionCount += section.subsections.filter(sub => 
        completedSections.includes(sub.id)
      ).length
    }
    return count + sectionCount
  }, 0)
  
  return Math.round((completed / totalSections) * 100)
}

/**
 * 混合进度计算策略 - 结合多种指标
 */
export function calculateAdvancedProgress(
  sections: ChapterSection[],
  progressData: LearningProgress,
  options: {
    scrollWeight: number // 滚动权重 (0.2)
    timeWeight: number // 时间权重 (0.5) 
    manualWeight: number // 手动权重 (0.3)
    minTimeThreshold: number // 最小时间阈值(秒)
  } = {
    scrollWeight: 0.2,
    timeWeight: 0.5,
    manualWeight: 0.3,
    minTimeThreshold: 30
  }
): number {
  if (sections.length === 0) return 0

  // 1. 滚动进度分数 (0-20分)
  const scrollScore = Math.min(progressData.scrollProgress || 0, 100) * options.scrollWeight

  // 2. 时间进度分数 (0-50分)  
  const totalEstimatedTime = sections.reduce((sum, section) => 
    sum + section.estimatedTime + (section.subsections?.reduce((subSum, sub) => 
      subSum + sub.estimatedTime, 0) || 0), 0)
  
  const timeScore = totalEstimatedTime > 0 ? 
    Math.min((progressData.totalTimeSpent / totalEstimatedTime) * 100, 100) * options.timeWeight : 0

  // 3. 手动标记进度分数 (0-30分)
  const manualScore = calculateProgressPercentage(sections, progressData.completedSections) * options.manualWeight

  // 4. 章节观看时间加权分数 (0-20分)
  let timeBonus = 0
  if (progressData.timeInView) {
    Object.entries(progressData.timeInView).forEach(([sectionId, timeSpent]) => {
      if (timeSpent >= options.minTimeThreshold) {
        timeBonus += Math.min(timeSpent / 60, 3) // 每个章节最多3分，基于观看时间
      }
    })
  }

  const combinedScore = scrollScore + timeScore + manualScore + Math.min(timeBonus, 20)
  
  return Math.min(Math.round(combinedScore), 100)
}

/**
 * 智能章节完成检测
 */
export function detectSectionCompletion(
  sectionId: string,
  timeInView: number,
  scrollProgress: number,
  estimatedTime: number
): boolean {
  // 完成条件：
  // 1. 观看时间 >= 预估时间的60% 且 >= 20秒
  // 2. 滚动进度 >= 70% 或 观看时间 >= 预估时间的90%
  const timeThreshold = Math.max(estimatedTime * 0.6 * 60, 20) // 转换为秒
  const timeComplete = timeInView >= timeThreshold
  const scrollComplete = scrollProgress >= 70
  const deepReadingComplete = timeInView >= (estimatedTime * 0.9 * 60)
  
  return timeComplete && (scrollComplete || deepReadingComplete)
}

/**
 * 获取下一个要学习的章节
 */
export function getNextSection(
  sections: ChapterSection[],
  completedSections: string[]
): ChapterSection | null {
  for (const section of sections) {
    if (!completedSections.includes(section.id)) {
      return section
    }
    if (section.subsections) {
      for (const subsection of section.subsections) {
        if (!completedSections.includes(subsection.id)) {
          return subsection
        }
      }
    }
  }
  return null
}

/**
 * 滚动到指定章节
 */
export function scrollToSection(sectionId: string, offset: number = 100) {
  if (typeof window === 'undefined') return
  
  // 尝试多种选择器
  const selectors = [
    `[data-section="${sectionId}"]`,
    `[data-subsection="${sectionId}"]`,
    `#${sectionId}`,
    `#section-${sectionId}`
  ]
  
  for (const selector of selectors) {
    const element = document.querySelector(selector)
    if (element) {
      const top = element.getBoundingClientRect().top + window.pageYOffset - offset
      window.scrollTo({
        top: top,
        behavior: 'smooth'
      })
      break
    }
  }
}

/**
 * 设置章节可见性监听器
 */
export function setupSectionObserver(
  sections: ChapterSection[],
  onSectionChange: (sectionId: string) => void,
  threshold: number = 0.5
): IntersectionObserver | null {
  if (typeof window === 'undefined') return null
  
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section') ||
                           entry.target.getAttribute('data-subsection') ||
                           entry.target.id
          if (sectionId) {
            onSectionChange(sectionId)
          }
        }
      })
    },
    {
      threshold: threshold,
      rootMargin: '-100px 0px -100px 0px'
    }
  )
  
  // 观察所有章节元素
  sections.forEach((section) => {
    if (section.element) {
      observer.observe(section.element)
    }
    section.subsections?.forEach((subsection) => {
      if (subsection.element) {
        observer.observe(subsection.element)
      }
    })
  })
  
  return observer
}

/**
 * 格式化学习时间
 */
export function formatLearningTime(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} 分钟`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  if (remainingMinutes === 0) {
    return `${hours} 小时`
  }
  return `${hours} 小时 ${remainingMinutes} 分钟`
}

/**
 * 本地存储学习进度
 */
export class LearningProgressStore {
  private static getKey(tutorialId: number): string {
    return `learning_progress_${tutorialId}`
  }
  
  static save(tutorialId: number, progress: Partial<LearningProgress>): void {
    if (typeof window === 'undefined') return
    
    try {
      const existing = this.load(tutorialId)
      const updated = { ...existing, ...progress, tutorialId }
      localStorage.setItem(this.getKey(tutorialId), JSON.stringify(updated))
    } catch (error) {
      console.warn('Failed to save learning progress:', error)
    }
  }
  
  static load(tutorialId: number): LearningProgress {
    if (typeof window === 'undefined') {
      return this.getDefault(tutorialId)
    }
    
    try {
      const stored = localStorage.getItem(this.getKey(tutorialId))
      if (stored) {
        return { ...this.getDefault(tutorialId), ...JSON.parse(stored) }
      }
    } catch (error) {
      console.warn('Failed to load learning progress:', error)
    }
    
    return this.getDefault(tutorialId)
  }
  
  static clear(tutorialId: number): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.removeItem(this.getKey(tutorialId))
    } catch (error) {
      console.warn('Failed to clear learning progress:', error)
    }
  }
  
  private static getDefault(tutorialId: number): LearningProgress {
    return {
      tutorialId,
      completedSections: [],
      currentSection: '',
      totalTimeSpent: 0,
      progressPercentage: 0,
      lastAccessed: new Date().toISOString(),
      sectionTimeSpent: {},
      
      // 新增字段默认值
      scrollProgress: 0,
      timeInView: {},
      interactionEvents: 0,
      readingSpeed: 200
    }
  }
}