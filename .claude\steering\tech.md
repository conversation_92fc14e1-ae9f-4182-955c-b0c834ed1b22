# 技术指导文档 (Tech Steering)

## 技术栈总览

### 前端技术栈
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript 5+
- **样式**: Tailwind CSS 3.4+
- **UI组件**: shadcn/ui (基于 Radix UI)
- **图标**: Lucide React
- **状态管理**: React Hooks + Context
- **表单处理**: React Hook Form + Zod 验证
- **编辑器**: TipTap 富文本编辑器

### 后端技术栈
- **数据库**: Supabase (PostgreSQL 15+)
- **API**: Next.js App Router API Routes
- **认证**: 自定义认证系统 + bcryptjs
- **文件存储**: Supabase Storage
- **实时功能**: Supabase Realtime (可选)

### 开发工具
- **包管理**: pnpm (优先)
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript strict mode
- **Git工作流**: Feature branch + PR review

## 批准的技术库

### 必须使用的库
```json
{
  "next": "14.2.30",
  "react": "^18",
  "typescript": "^5",
  "tailwindcss": "^3.4.17",
  "@supabase/supabase-js": "^2.52.1",
  "lucide-react": "^0.454.0",
  "@radix-ui/react-*": "latest",
  "class-variance-authority": "^0.7.1",
  "clsx": "^2.1.1",
  "tailwind-merge": "^2.5.5"
}
```

### 推荐使用的库
```json
{
  "react-hook-form": "^7.54.1",
  "@hookform/resolvers": "^3.9.1",
  "zod": "^3.24.1",
  "@tiptap/react": "^3.0.7",
  "@tiptap/starter-kit": "^3.0.7",
  "@tiptap/extension-image": "^3.0.7",
  "@tiptap/extension-link": "^3.0.7",
  "@tiptap/extension-code-block-lowlight": "^3.0.7",
  "bcryptjs": "^3.0.2",
  "date-fns": "4.1.0",
  "next-themes": "^0.4.4",
  "sonner": "^1.7.1"
}
```

### 禁止使用的库
- ❌ jQuery (使用 React 替代)
- ❌ Bootstrap (使用 Tailwind CSS)
- ❌ Material-UI (使用 shadcn/ui)
- ❌ Axios (使用 fetch API)
- ❌ Moment.js (使用 date-fns)
- ❌ Lodash (使用原生 JavaScript)

## 架构约束

### 文件夹结构
```
app/                    # Next.js App Router
├── api/               # API 路由 (RESTful 设计)
├── (pages)/           # 页面组件
└── globals.css        # 全局样式

components/            # React 组件
├── ui/               # shadcn/ui 基础组件
├── admin/            # 管理后台专用组件
├── editor/           # 编辑器组件
└── learning/         # 学习功能组件

lib/                  # 工具函数库
├── supabase.ts       # Supabase 客户端
├── auth.ts           # 认证相关工具
├── database.ts       # 数据库操作
└── utils.ts          # 通用工具函数

hooks/                # 自定义 React Hooks
types/                # TypeScript 类型定义
```

### 命名约定
- **组件**: PascalCase (`TutorialCard.tsx`)
- **文件**: kebab-case (`tutorial-card.tsx`)
- **函数**: camelCase (`getUserProfile`)
- **常量**: UPPER_SNAKE_CASE (`API_BASE_URL`)
- **CSS类**: kebab-case (`tutorial-card`)

### 代码规范

#### TypeScript 规范
```typescript
// ✅ 好的做法
interface TutorialProps {
  id: number
  title: string
  content: string
  isUnlocked: boolean
}

const Tutorial: React.FC<TutorialProps> = ({ 
  id, 
  title, 
  content, 
  isUnlocked 
}) => {
  // 组件实现
}

// ❌ 避免的做法
const Tutorial = (props: any) => {
  // 缺乏类型安全
}
```

#### React 组件规范
```typescript
// ✅ 推荐的组件结构
"use client" // 仅在需要时使用

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface ComponentProps {
  // 类型定义
}

export function Component({ prop1, prop2 }: ComponentProps) {
  // 1. 状态定义
  const [state, setState] = useState()
  
  // 2. 副作用
  useEffect(() => {
    // 副作用逻辑
  }, [])
  
  // 3. 事件处理函数
  const handleClick = () => {
    // 事件处理逻辑
  }
  
  // 4. 渲染
  return (
    <Card>
      <Button onClick={handleClick}>
        {prop1}
      </Button>
    </Card>
  )
}
```

#### API 路由规范
```typescript
// ✅ 标准 API 路由结构
import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

export async function GET(request: NextRequest) {
  try {
    // 1. 参数验证
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    // 2. 业务逻辑
    const { data, error } = await supabaseAdmin
      .from('tutorials')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: "教程不存在" }, 
        { status: 404 }
      )
    }
    
    // 3. 返回响应
    return NextResponse.json({
      success: true,
      data
    })
  } catch (error) {
    console.error("API错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" }, 
      { status: 500 }
    )
  }
}
```

## 数据库设计原则

### 表设计规范
```sql
-- ✅ 推荐的表结构
CREATE TABLE tutorials (
  id BIGSERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT,
  category_id BIGINT REFERENCES categories(id),
  tags TEXT[] DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'draft',
  price DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 索引设计
CREATE INDEX idx_tutorials_category ON tutorials(category_id);
CREATE INDEX idx_tutorials_status ON tutorials(status);
CREATE INDEX idx_tutorials_created_at ON tutorials(created_at);
```

### 查询优化
```typescript
// ✅ 优化的查询
const { data, error } = await supabase
  .from('tutorials')
  .select(`
    id,
    title,
    description,
    categories!category_id(name),
    tags,
    price,
    created_at
  `)
  .eq('status', 'published')
  .order('created_at', { ascending: false })
  .limit(20)

// ❌ 避免的查询
const { data, error } = await supabase
  .from('tutorials')
  .select('*') // 避免 SELECT *
```

## 安全约束

### 认证与授权
```typescript
// ✅ 安全的认证检查
export async function authenticateAdmin(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader) {
    throw new Error('未提供认证信息')
  }
  
  // 验证token逻辑
  return isValidToken(authHeader)
}

// ✅ 输入验证
import { z } from 'zod'

const TutorialSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().max(1000),
  content: z.string(),
  price: z.number().min(0)
})
```

### 数据保护
```typescript
// ✅ 敏感数据处理
const hashPassword = async (password: string) => {
  const saltRounds = 12
  return await bcrypt.hash(password, saltRounds)
}

// ✅ 防止 SQL 注入（使用 Supabase ORM）
const { data } = await supabase
  .from('tutorials')
  .select('*')
  .eq('id', tutorialId) // 自动转义
```

## 性能约束

### 前端性能
- **Bundle Size**: 初始加载 < 500KB
- **首屏渲染**: < 2秒 (3G网络)
- **交互响应**: < 100ms
- **图片优化**: 使用 next/image

### 后端性能
- **API响应时间**: < 200ms (95th percentile)
- **数据库查询**: < 100ms (平均)
- **内存使用**: < 512MB (单实例)
- **并发处理**: 支持100+并发用户

### 优化技术
```typescript
// ✅ 代码分割
const LazyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <div>加载中...</div>
})

// ✅ 图片优化
import Image from "next/image"

<Image
  src="/tutorial-image.jpg"
  alt="教程图片"
  width={400}
  height={300}
  priority={false}
/>

// ✅ 实时数据同步策略（已优化）
// 针对关键数据使用无缓存策略确保实时性
return NextResponse.json(response, {
  headers: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache', 
    'Expires': '0',
    'X-Cache-Strategy': 'no-cache',
    'X-Timestamp': new Date().toISOString()
  }
})

// ✅ 强制刷新机制
const forceRefresh = (url: string) => {
  return fetch(`${url}?_t=${Date.now()}`, {
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}
```

## 环境配置

### 开发环境
```bash
# 必需的环境变量
DATABASE_URL="postgresql://..."
NEXT_PUBLIC_SUPABASE_URL="https://..."
NEXT_PUBLIC_SUPABASE_ANON_KEY="..."
SUPABASE_SERVICE_ROLE_KEY="..."
NODE_ENV="development"
```

### 生产环境
```bash
# 额外的生产环境变量
NEXTAUTH_SECRET="..."
NEXTAUTH_URL="https://..."
WEBHOOK_SECRET="..."
```

## 部署策略

### Vercel 部署 (推荐)
```json
// vercel.json
{
  "buildCommand": "pnpm build",
  "devCommand": "pnpm dev",
  "installCommand": "pnpm install",
  "framework": "nextjs",
  "regions": ["sin1", "hkg1"]
}
```

### Docker 部署 (备选)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN pnpm install --frozen-lockfile
COPY . .
RUN pnpm build
EXPOSE 3000
CMD ["pnpm", "start"]
```

## 监控与日志

### 错误监控
```typescript
// ✅ 结构化日志
console.error('API错误:', {
  endpoint: '/api/tutorials',
  error: error.message,
  userId: user?.id,
  timestamp: new Date().toISOString()
})

// ✅ 性能监控
console.time('database-query')
const result = await supabase.from('tutorials').select()
console.timeEnd('database-query')
```

## 技术债务管理

### 代码质量检查
```bash
# 运行质量检查
pnpm lint          # ESLint 检查
pnpm type-check    # TypeScript 检查
pnpm test          # 单元测试
pnpm build         # 构建验证
```

### 依赖管理
```bash
# 定期更新依赖
pnpm outdated      # 检查过时依赖
pnpm update        # 安全更新
pnpm audit         # 安全审计
```

## 升级路径

### Next.js 版本升级
1. 检查 [Next.js 升级指南](https://nextjs.org/docs/upgrading)
2. 运行 `npx @next/codemod` 自动迁移
3. 测试所有功能
4. 更新文档

### 依赖升级策略
- **重大版本**: 需要团队讨论和充分测试
- **次要版本**: 定期更新（每月）
- **补丁版本**: 及时更新（每周）