/**
 * 测试后台统计数据准确性
 * 验证总解锁次数和总营收计算是否正确
 */

const { createClient } = require('@supabase/supabase-js')

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testStatsAccuracy() {
  try {
    console.log('🔍 开始验证统计数据准确性...\n')

    // 1. 测试总教程数
    const { count: tutorialCount, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select('id', { count: 'exact', head: true })
    
    if (tutorialError) throw tutorialError
    console.log(`📚 总教程数: ${tutorialCount}`)

    // 2. 测试总密钥数
    const { count: totalKeysCount, error: totalKeysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select('id', { count: 'exact', head: true })
    
    if (totalKeysError) throw totalKeysError
    console.log(`🔑 总密钥数: ${totalKeysCount}`)

    // 3. 测试已使用密钥数
    const { count: usedKeysCount, error: usedKeysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'used')
    
    if (usedKeysError) throw usedKeysError
    console.log(`✅ 已使用密钥数: ${usedKeysCount}`)

    // 4. 测试总解锁次数（应该等于user_unlocks表记录数）
    const { count: unlocksCount, error: unlocksError } = await supabaseAdmin
      .from('user_unlocks')
      .select('id', { count: 'exact', head: true })
    
    if (unlocksError) throw unlocksError
    console.log(`🔓 总解锁次数: ${unlocksCount}`)

    // 5. 详细分析解锁记录
    const { data: unlocksDetail, error: unlocksDetailError } = await supabaseAdmin
      .from('user_unlocks')
      .select(`
        id,
        tutorial_id,
        key_id,
        tutorials!inner(title, price)
      `)
    
    if (unlocksDetailError) throw unlocksDetailError
    console.log(`\n📊 解锁记录详情:`)
    unlocksDetail?.forEach((unlock, index) => {
      console.log(`  ${index + 1}. 教程: ${unlock.tutorials.title} | 价格: ¥${unlock.tutorials.price} | 密钥ID: ${unlock.key_id}`)
    })

    // 6. 计算总营收（基于已使用密钥）
    const { data: revenueData, error: revenueError } = await supabaseAdmin
      .from('tutorial_keys')
      .select(`
        id,
        tutorial_id,
        status,
        tutorials!inner(title, price)
      `)
      .eq('status', 'used')

    if (revenueError) throw revenueError

    console.log(`\n💰 已使用密钥详情:`)
    let totalRevenue = 0
    revenueData?.forEach((key, index) => {
      console.log(`  ${index + 1}. 密钥ID: ${key.id} | 教程: ${key.tutorials.title} | 价格: ¥${key.tutorials.price}`)
      totalRevenue += key.tutorials.price || 0
    })

    console.log(`\n💵 总营收: ¥${totalRevenue}`)

    // 7. 验证数据一致性
    console.log(`\n🔍 数据一致性验证:`)
    console.log(`  - 已使用密钥数量 = 总解锁次数? ${usedKeysCount === unlocksCount ? '✅' : '❌'} (${usedKeysCount} vs ${unlocksCount})`)
    
    // 8. 调用API验证
    console.log(`\n🌐 API统计数据验证:`)
    const response = await fetch('http://localhost:3002/api/admin/stats')
    const apiStats = await response.json()
    
    console.log(`API返回数据:`, apiStats)
    console.log(`数据对比:`)
    console.log(`  - 总教程数: API=${apiStats.total_tutorials}, 实际=${tutorialCount} ${apiStats.total_tutorials === tutorialCount ? '✅' : '❌'}`)
    console.log(`  - 总密钥数: API=${apiStats.total_keys}, 实际=${totalKeysCount} ${apiStats.total_keys === totalKeysCount ? '✅' : '❌'}`)
    console.log(`  - 已使用密钥: API=${apiStats.used_keys}, 实际=${usedKeysCount} ${apiStats.used_keys === usedKeysCount ? '✅' : '❌'}`)
    console.log(`  - 总解锁次数: API=${apiStats.total_unlocks}, 实际=${unlocksCount} ${apiStats.total_unlocks === unlocksCount ? '✅' : '❌'}`)
    console.log(`  - 总营收: API=¥${apiStats.total_revenue}, 实际=¥${totalRevenue} ${apiStats.total_revenue === totalRevenue ? '✅' : '❌'}`)

  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 运行测试
testStatsAccuracy()