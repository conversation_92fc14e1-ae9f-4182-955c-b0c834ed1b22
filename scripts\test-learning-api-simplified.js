#!/usr/bin/env node

/**
 * 测试学习进度API（简化版）
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function testLearningProgressAPI() {
  console.log('🧪 测试学习进度API（简化版）...')
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    // 首先查找一个真实存在的教程
    const { data: existingTutorial, error: tutorialError } = await supabase
      .from('tutorials')
      .select('id')
      .limit(1)
      .single()

    if (tutorialError || !existingTutorial) {
      console.log('❌ 没有找到现有教程，无法测试')
      return
    }

    const testTutorialId = existingTutorial.id
    console.log('📖 使用教程ID进行测试:', testTutorialId)

    // 首先确保有测试用的教程和解锁记录
    const testUser = 'test_api_user_' + Date.now()

    // 创建测试解锁记录
    console.log('📝 创建测试解锁记录...')
    
    // 首先查找现有的密钥
    const { data: existingKey, error: keyError } = await supabase
      .from('tutorial_keys')
      .select('id')
      .eq('tutorial_id', testTutorialId)
      .limit(1)
      .single()

    let keyId = existingKey?.id

    // 如果没有可用的密钥，创建一个测试密钥
    if (!keyId) {
      console.log('📋 创建测试密钥...')
      const testKeyCode = 'TEST' + Math.random().toString(36).substr(2, 20).toUpperCase()
      
      const { data: newKey, error: createKeyError } = await supabase
        .from('tutorial_keys')
        .insert({
          tutorial_id: testTutorialId,
          key_code: testKeyCode,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (createKeyError) {
        console.log('❌ 创建测试密钥失败:', createKeyError.message)
        return
      }

      keyId = newKey.id
      console.log('✅ 测试密钥创建成功，ID:', keyId)
    }

    const { data: unlockData, error: unlockError } = await supabase
      .from('user_unlocks')
      .insert({
        user_identifier: testUser,
        tutorial_id: testTutorialId,
        key_id: keyId,
        unlocked_at: new Date().toISOString()
      })
      .select()
      .single()

    if (unlockError) {
      console.log('❌ 创建测试解锁记录失败:', unlockError.message)
      return
    }

    console.log('✅ 测试解锁记录创建成功，ID:', unlockData.id)

    // 测试API调用
    console.log('\n🚀 测试学习进度API...')
    
    const testProgress = {
      tutorialId: testTutorialId,
      status: 'in_progress',
      progressPercentage: 45,
      timeSpent: 25,
      interactionData: {
        scrollPercentage: 75,
        interactionCount: 10,
        pauseCount: 2,
        deviceType: 'web',
        completedSections: ['section-1', 'section-2', 'section-3'],
        currentSection: 'section-4'
      }
    }

    const response = await fetch('http://localhost:3000/api/learning/progress', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'X-User-Identifier': testUser // 模拟用户身份
      },
      body: JSON.stringify(testProgress)
    })

    const result = await response.json()
    
    if (response.ok && result.success) {
      console.log('✅ API调用成功!')
      console.log('📊 返回数据:', JSON.stringify(result, null, 2))
      
      if (result.achievements && result.achievements.length > 0) {
        console.log('🏆 获得成就:', result.achievements.map(a => a.display_name).join(', '))
      }
    } else {
      console.log('❌ API调用失败:', result.error)
      console.log('详情:', result.details)
    }

    // 测试获取学习统计
    console.log('\n📊 测试获取学习统计...')
    const statsResponse = await fetch(`http://localhost:3000/api/learning/progress?user=${testUser}&tutorialId=${testTutorialId}`)
    const statsResult = await statsResponse.json()

    if (statsResponse.ok && statsResult.success) {
      console.log('✅ 统计查询成功!')
      console.log('📈 统计数据:', JSON.stringify(statsResult.data.userStats, null, 2))
    } else {
      console.log('❌ 统计查询失败:', statsResult.error)
    }

    // 清理测试数据
    console.log('\n🧹 清理测试数据...')
    await supabase
      .from('user_unlocks')
      .delete()
      .eq('id', unlockData.id)
    
    console.log('✅ 测试数据已清理')

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }
}

// 测试数据库修改
async function testDatabaseModification() {
  console.log('🔍 检查数据库修改...')
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    // 检查 learning_data 字段是否存在
    const { data, error } = await supabase
      .from('user_unlocks')
      .select('learning_data')
      .limit(1)

    if (error) {
      console.log('❌ learning_data 字段不存在或有错误:', error.message)
      console.log('💡 请在 Supabase SQL Editor 中执行: scripts/09-add-learning-data-to-user-unlocks.sql')
    } else {
      console.log('✅ learning_data 字段已存在且可访问')
    }

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error)
  }
}

// 执行所有测试
async function runAllTests() {
  console.log('🚀 开始学习进度系统测试（简化版）\n')
  
  await testDatabaseModification()
  
  // 只有在数据库修改正确时才测试API
  console.log('\n' + '='.repeat(50))
  console.log('✅ 数据库字段已确认存在，开始测试API功能...')
  console.log('='.repeat(50))
  
  // 现在运行完整的API测试
  await testLearningProgressAPI()
  
  console.log('\n✨ 数据库检查完成')
}

runAllTests().catch(console.error)