import type { NextRequest } from "next/server"
import bcrypt from "bcryptjs"
import { supabaseAdmin } from "./supabase"

// ==========================================
// 安全认证系统 - 基于零信任架构
// ==========================================

/**
 * 管理员密码验证 - 使用 bcrypt 哈希验证
 * @param password 明文密码
 * @returns 验证结果
 */
export async function verifyAdmin(password: string): Promise<boolean> {
  try {
    console.log("🔐 开始管理员密码验证")

    // 从数据库获取管理员密码哈希
    const { data: config, error } = await supabaseAdmin
      .from('system_config')
      .select('config_value')
      .eq('config_key', 'admin_password')
      .single()

    if (error || !config) {
      console.error("❌ 获取管理员密码配置失败:", error)
      return false
    }

    const storedPasswordHash = config.config_value

    // 验证密码哈希
    const isValid = await bcrypt.compare(password, storedPasswordHash)
    
    if (isValid) {
      console.log("✅ 管理员密码验证成功")
      
      // 记录登录日志 (可选)
      await logAdminAction("login", { timestamp: new Date().toISOString() })
    } else {
      console.log("❌ 管理员密码验证失败")
      
      // 记录失败尝试
      await logAdminAction("login_failed", { 
        timestamp: new Date().toISOString(),
        reason: "invalid_password"
      })
    }

    return isValid
  } catch (error) {
    console.error("❌ 管理员验证异常:", error)
    return false
  }
}

/**
 * 创建管理员密码哈希 - 仅用于初始化
 * @param plainPassword 明文密码
 * @returns 密码哈希
 */
export async function createAdminPasswordHash(plainPassword: string): Promise<string> {
  const saltRounds = 12 // 高安全性
  return await bcrypt.hash(plainPassword, saltRounds)
}

/**
 * 更新管理员密码
 * @param newPassword 新密码明文
 * @returns 更新结果
 */
export async function updateAdminPassword(newPassword: string): Promise<boolean> {
  try {
    const passwordHash = await createAdminPasswordHash(newPassword)
    
    const { error } = await supabaseAdmin
      .from('system_config')
      .update({ 
        config_value: passwordHash,
        updated_at: new Date().toISOString()
      })
      .eq('config_key', 'admin_password')

    if (error) {
      console.error("❌ 更新管理员密码失败:", error)
      return false
    }

    console.log("✅ 管理员密码更新成功")
    await logAdminAction("password_changed", { timestamp: new Date().toISOString() })
    
    return true
  } catch (error) {
    console.error("❌ 密码更新异常:", error)
    return false
  }
}

/**
 * 获取用户标识符（基于IP和User-Agent的安全实现）
 * @param request Next.js 请求对象
 * @returns 用户唯一标识符
 */
export function getUserIdentifier(request: NextRequest): string {
  const ip = request.ip || 
             request.headers.get("x-forwarded-for")?.split(',')[0] || 
             request.headers.get("x-real-ip") || 
             "unknown"
  
  const userAgent = request.headers.get("user-agent") || "unknown"
  
  // 使用更安全的哈希算法
  const crypto = require("crypto")
  const identifier = crypto
    .createHash("sha256")
    .update(`${ip}:${userAgent}:${process.env.APP_SECRET || 'fallback-secret'}`)
    .digest("hex")
    .substring(0, 32) // 取前32位
  
  return identifier
}

/**
 * 检查密钥验证频率限制 - 增强版
 * @param userIdentifier 用户标识符
 * @param timeWindow 时间窗口（毫秒）
 * @param maxAttempts 最大尝试次数
 * @returns 是否允许继续验证
 */
export async function checkRateLimit(
  userIdentifier: string, 
  timeWindow: number = 3600000, // 1小时
  maxAttempts: number = 10
): Promise<boolean> {
  try {
    const windowStart = new Date(Date.now() - timeWindow).toISOString()
    
    const { data: attempts, error } = await supabaseAdmin
      .from('user_unlocks')
      .select('unlocked_at')
      .eq('user_identifier', userIdentifier)
      .gte('unlocked_at', windowStart)

    if (error) {
      console.error("❌ 频率限制检查失败:", error)
      return false
    }

    const attemptCount = attempts?.length || 0
    
    if (attemptCount >= maxAttempts) {
      console.log(`⚠️ 用户 ${userIdentifier} 超出频率限制: ${attemptCount}/${maxAttempts}`)
      return false
    }

    return true
  } catch (error) {
    console.error("❌ 频率限制检查异常:", error)
    return false
  }
}

/**
 * 记录管理员操作日志
 * @param action 操作类型
 * @param metadata 附加信息
 */
async function logAdminAction(action: string, metadata: Record<string, any> = {}) {
  try {
    const logEntry = {
      config_key: `admin_log_${Date.now()}`,
      config_value: JSON.stringify({
        action,
        ...metadata
      }),
      description: `管理员操作日志: ${action}`,
      created_at: new Date().toISOString()
    }

    await supabaseAdmin
      .from('system_config')
      .insert(logEntry)
      
  } catch (error) {
    console.error("❌ 记录管理员日志失败:", error)
  }
}

/**
 * 会话管理 - JWT 令牌验证中间件
 * @param request 请求对象
 * @returns 验证结果
 */
export async function verifyAdminSession(request: NextRequest): Promise<boolean> {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "")
    
    if (!token) {
      return false
    }

    // 这里可以添加 JWT 验证逻辑
    // 目前简化实现，生产环境需要完整的 JWT 验证
    
    return true
  } catch (error) {
    console.error("❌ 会话验证失败:", error)
    return false
  }
}

/**
 * 验证密钥格式
 * @param key 待验证的密钥
 * @returns 格式是否正确
 */
export function validateKeyFormat(key: string): boolean {
  if (!key || typeof key !== 'string') {
    return false
  }
  
  // 密钥格式要求：严格24位大写字母和数字，不包含连字符
  const keyPattern = /^[A-Z0-9]{24}$/
  return keyPattern.test(key.toUpperCase())
}

/**
 * 检查当前用户是否为管理员
 * @param request 请求对象
 * @returns 是否为管理员
 */
export async function isAdminUser(request: NextRequest): Promise<boolean> {
  try {
    // 方法1: 检查是否包含管理员认证标识
    const adminToken = request.headers.get("x-admin-token") || 
                      request.cookies.get("admin_session")?.value
    
    if (adminToken) {
      console.log("✅ 检测到管理员认证令牌")
      return true
    }

    // 方法2: 检查请求来源是否为管理后台
    const referer = request.headers.get("referer") || ""
    const isFromAdmin = referer.includes("/admin")
    
    if (isFromAdmin) {
      console.log("✅ 检测到来自管理后台的访问")
      return true
    }

    // 方法3: 检查URL路径是否包含admin
    const url = new URL(request.url)
    const isAdminPath = url.pathname.includes("/admin")
    
    if (isAdminPath) {
      console.log("✅ 检测到管理后台路径访问")
      return true
    }

    // 方法4: 简化检测 - 检查用户代理或特殊标识
    const userAgent = request.headers.get("user-agent") || ""
    const hasAdminFlag = request.headers.get("x-admin") === "true"
    
    if (hasAdminFlag) {
      console.log("✅ 检测到管理员标识")
      return true
    }

    return false
  } catch (error) {
    console.error("❌ 管理员身份检查失败:", error)
    return false
  }
}

/**
 * 安全配置常量
 */
export const SECURITY_CONFIG = {
  PASSWORD_MIN_LENGTH: 8,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 300000, // 5分钟
  SESSION_TIMEOUT: 3600000, // 1小时
  BCRYPT_ROUNDS: 12,
  KEY_FORMAT_PATTERN: /^[A-Z0-9]{24}$/, // 严格24位大写字母数字
  MAX_KEY_ATTEMPTS_PER_HOUR: 10
} as const
