/**
 * 自动学习进度跟踪器 - 增强版
 * 
 * 功能：
 * 1. 自动检测页面进入/离开状态
 * 2. 实时滚动进度跟踪
 * 3. 分钟级自动更新
 * 4. 混合进度计算（滚动+时间+章节）
 * 5. 智能章节完成检测
 */

import { LearningProgress, ChapterSection, calculateAdvancedProgress, detectSectionCompletion } from './learning-utils'

export interface AutoLearningConfig {
  tutorialId: number
  sections: ChapterSection[]
  onProgressUpdate: (progress: LearningProgress) => void
  onSectionComplete: (sectionId: string) => void
  updateInterval?: number // 更新间隔(毫秒)，默认60秒
  scrollUpdateThreshold?: number // 滚动更新阈值(像素)，默认100px
}

export class AutoLearningTracker {
  private config: AutoLearningConfig
  private isActive: boolean = false
  private startTime: number = 0
  private lastScrollY: number = 0
  private lastUpdateTime: number = 0
  private sessionTimeSpent: number = 0
  private currentScrollProgress: number = 0
  private sectionViewStartTimes: Map<string, number> = new Map()
  private currentVisibleSection: string = ''
  
  // 定时器和事件监听器
  private updateTimer?: NodeJS.Timeout
  private visibilityListener?: () => void
  private scrollListener?: () => void
  private beforeUnloadListener?: () => void
  private intersectionObserver?: IntersectionObserver

  // 当前进度数据
  private progressData: LearningProgress

  constructor(config: AutoLearningConfig) {
    this.config = {
      updateInterval: 60000, // 默认60秒
      scrollUpdateThreshold: 100,
      ...config
    }
    
    // 初始化进度数据
    this.progressData = this.loadInitialProgress()
    
    console.log('🎯 自动学习跟踪器初始化:', this.config.tutorialId)
  }

  /**
   * 启动自动跟踪
   */
  public start(): void {
    if (this.isActive) return
    
    console.log('▶️ 启动自动学习跟踪')
    this.isActive = true
    this.startTime = Date.now()
    this.lastUpdateTime = Date.now()
    
    // 设置事件监听器
    this.setupEventListeners()
    
    // 设置定时更新
    this.startUpdateTimer()
    
    // 设置章节可见性监听
    this.setupSectionObserver()
    
    // 立即更新一次状态
    this.updateProgress()
  }

  /**
   * 停止自动跟踪
   */
  public stop(): void {
    if (!this.isActive) return
    
    console.log('⏹️ 停止自动学习跟踪')
    this.isActive = false
    
    // 最后一次更新
    this.updateProgress()
    
    // 清理监听器和定时器
    this.cleanup()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (typeof window === 'undefined') return

    // 页面可见性变化监听
    this.visibilityListener = () => {
      if (document.hidden) {
        console.log('📱 页面不可见，暂停跟踪')
        this.pauseTracking()
      } else {
        console.log('👀 页面可见，恢复跟踪')
        this.resumeTracking()
      }
    }
    document.addEventListener('visibilitychange', this.visibilityListener)

    // 滚动监听
    this.scrollListener = this.throttle(() => {
      this.updateScrollProgress()
    }, 500) // 500ms节流
    window.addEventListener('scroll', this.scrollListener, { passive: true })

    // 页面卸载前保存
    this.beforeUnloadListener = () => {
      this.updateProgress()
    }
    window.addEventListener('beforeunload', this.beforeUnloadListener)
  }

  /**
   * 设置章节可见性监听器
   */
  private setupSectionObserver(): void {
    if (typeof window === 'undefined') return

    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const sectionId = this.getSectionId(entry.target)
          if (!sectionId) return

          if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
            // 章节进入视野
            this.onSectionEnterView(sectionId)
          } else if (!entry.isIntersecting && this.sectionViewStartTimes.has(sectionId)) {
            // 章节离开视野
            this.onSectionLeaveView(sectionId)
          }
        })
      },
      {
        threshold: [0.1, 0.5, 0.9],
        rootMargin: '-50px 0px -50px 0px'
      }
    )

    // 监听所有章节元素
    this.config.sections.forEach(section => {
      if (section.element) {
        this.intersectionObserver!.observe(section.element)
      }
      section.subsections?.forEach(subsection => {
        if (subsection.element) {
          this.intersectionObserver!.observe(subsection.element)
        }
      })
    })
  }

  /**
   * 章节进入视野
   */
  private onSectionEnterView(sectionId: string): void {
    console.log(`👁️ 章节进入视野: ${sectionId}`)
    this.sectionViewStartTimes.set(sectionId, Date.now())
    this.currentVisibleSection = sectionId
    
    // 更新当前章节
    this.progressData.currentSection = sectionId
    this.saveProgress()
  }

  /**
   * 章节离开视野
   */
  private onSectionLeaveView(sectionId: string): void {
    const startTime = this.sectionViewStartTimes.get(sectionId)
    if (!startTime) return

    const viewTime = Math.floor((Date.now() - startTime) / 1000) // 秒
    console.log(`👁️ 章节离开视野: ${sectionId}, 观看时间: ${viewTime}秒`)

    // 记录观看时间
    if (!this.progressData.timeInView) {
      this.progressData.timeInView = {}
    }
    this.progressData.timeInView[sectionId] = (this.progressData.timeInView[sectionId] || 0) + viewTime

    // 检查是否自动完成章节
    const section = this.findSection(sectionId)
    if (section && !this.progressData.completedSections.includes(sectionId)) {
      const isComplete = detectSectionCompletion(
        sectionId,
        this.progressData.timeInView[sectionId],
        this.currentScrollProgress,
        section.estimatedTime
      )

      if (isComplete) {
        console.log(`✅ 章节自动完成: ${sectionId}`)
        this.progressData.completedSections.push(sectionId)
        this.config.onSectionComplete(sectionId)
      }
    }

    this.sectionViewStartTimes.delete(sectionId)
    this.saveProgress()
  }

  /**
   * 更新滚动进度
   */
  private updateScrollProgress(): void {
    if (typeof window === 'undefined') return

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight
    const clientHeight = document.documentElement.clientHeight
    
    const maxScroll = scrollHeight - clientHeight
    const scrollProgress = maxScroll > 0 ? Math.min((scrollTop / maxScroll) * 100, 100) : 0
    
    // 只有显著变化时才更新
    if (Math.abs(scrollProgress - this.currentScrollProgress) > 1) {
      this.currentScrollProgress = scrollProgress
      this.progressData.scrollProgress = scrollProgress
      
      console.log(`📜 滚动进度: ${scrollProgress.toFixed(1)}%`)
    }
  }

  /**
   * 暂停跟踪（页面不可见时）
   */
  private pauseTracking(): void {
    if (!this.isActive) return
    
    // 记录本次会话时间
    const sessionTime = Math.floor((Date.now() - this.lastUpdateTime) / 1000 / 60)
    this.sessionTimeSpent += sessionTime
    
    // 停止定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
      this.updateTimer = undefined
    }
  }

  /**
   * 恢复跟踪（页面可见时）
   */
  private resumeTracking(): void {
    if (!this.isActive) return
    
    this.lastUpdateTime = Date.now()
    this.startUpdateTimer()
  }

  /**
   * 启动定时更新
   */
  private startUpdateTimer(): void {
    if (this.updateTimer) return
    
    this.updateTimer = setInterval(() => {
      if (this.isActive && !document.hidden) {
        this.updateProgress()
      }
    }, this.config.updateInterval)
  }

  /**
   * 更新进度数据
   */
  private updateProgress(): void {
    if (!this.isActive) return

    const now = Date.now()
    const sessionTime = Math.floor((now - this.lastUpdateTime) / 1000 / 60) // 分钟
    
    // 更新总学习时间
    this.progressData.totalTimeSpent += sessionTime
    this.progressData.lastAccessed = new Date().toISOString()
    
    // 使用混合进度计算
    this.progressData.progressPercentage = calculateAdvancedProgress(
      this.config.sections,
      this.progressData,
      {
        scrollWeight: 0.3, // 滚动权重30%
        timeWeight: 0.4,   // 时间权重40%
        manualWeight: 0.3, // 手动权重30%
        minTimeThreshold: 30
      }
    )

    console.log(`📊 进度更新: ${this.progressData.progressPercentage}%, 时间: ${this.progressData.totalTimeSpent}分钟`)

    this.lastUpdateTime = now
    this.saveProgress()
    
    // 通知外部
    this.config.onProgressUpdate(this.progressData)
  }

  /**
   * 获取章节ID
   */
  private getSectionId(element: Element): string | null {
    return element.getAttribute('data-section') ||
           element.getAttribute('data-subsection') ||
           element.id ||
           null
  }

  /**
   * 查找章节
   */
  private findSection(sectionId: string): ChapterSection | undefined {
    for (const section of this.config.sections) {
      if (section.id === sectionId) return section
      if (section.subsections) {
        for (const subsection of section.subsections) {
          if (subsection.id === sectionId) return subsection
        }
      }
    }
    return undefined
  }

  /**
   * 加载初始进度
   */
  private loadInitialProgress(): LearningProgress {
    // 从localStorage加载或创建默认进度
    const defaultProgress: LearningProgress = {
      tutorialId: this.config.tutorialId,
      completedSections: [],
      currentSection: '',
      totalTimeSpent: 0,
      progressPercentage: 0,
      lastAccessed: new Date().toISOString(),
      sectionTimeSpent: {},
      scrollProgress: 0,
      timeInView: {},
      interactionEvents: 0,
      readingSpeed: 200
    }

    try {
      const stored = localStorage.getItem(`learning_progress_${this.config.tutorialId}`)
      if (stored) {
        return { ...defaultProgress, ...JSON.parse(stored) }
      }
    } catch (error) {
      console.warn('加载进度失败:', error)
    }

    return defaultProgress
  }

  /**
   * 保存进度到localStorage
   */
  private saveProgress(): void {
    try {
      localStorage.setItem(
        `learning_progress_${this.config.tutorialId}`,
        JSON.stringify(this.progressData)
      )
    } catch (error) {
      console.warn('保存进度失败:', error)
    }
  }

  /**
   * 节流函数
   */
  private throttle<T extends (...args: any[]) => any>(func: T, wait: number): T {
    let timeout: NodeJS.Timeout | null = null
    let previous = 0
    
    return ((...args: any[]) => {
      const now = Date.now()
      const remaining = wait - (now - previous)
      
      if (remaining <= 0 || remaining > wait) {
        if (timeout) {
          clearTimeout(timeout)
          timeout = null
        }
        previous = now
        func.apply(this, args)
      } else if (!timeout) {
        timeout = setTimeout(() => {
          previous = Date.now()
          timeout = null
          func.apply(this, args)
        }, remaining)
      }
    }) as T
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    // 清理定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
      this.updateTimer = undefined
    }

    // 清理事件监听器
    if (this.visibilityListener) {
      document.removeEventListener('visibilitychange', this.visibilityListener)
    }
    if (this.scrollListener) {
      window.removeEventListener('scroll', this.scrollListener)
    }
    if (this.beforeUnloadListener) {
      window.removeEventListener('beforeunload', this.beforeUnloadListener)
    }

    // 清理交叉观察器
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect()
    }

    // 清理状态
    this.sectionViewStartTimes.clear()
  }

  /**
   * 获取当前进度
   */
  public getProgress(): LearningProgress {
    return { ...this.progressData }
  }

  /**
   * 手动标记章节完成
   */
  public markSectionComplete(sectionId: string): void {
    if (!this.progressData.completedSections.includes(sectionId)) {
      this.progressData.completedSections.push(sectionId)
      this.updateProgress()
      this.config.onSectionComplete(sectionId)
    }
  }

  /**
   * 销毁跟踪器
   */
  public destroy(): void {
    this.stop()
    this.cleanup()
    console.log('🗑️ 自动学习跟踪器已销毁')
  }
}