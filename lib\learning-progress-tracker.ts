// 简化版实时学习进度跟踪器
import { 
  type ChapterSection, 
  type LearningProgress, 
  calculateAdvancedProgress,
  detectSectionCompletion,
  LearningProgressStore 
} from './learning-utils'

export class LearningProgressTracker {
  private updateInterval: NodeJS.Timeout | null = null
  private lastScrollPosition = 0
  private sectionTimers: Record<string, number> = {}
  private currentSection = ''
  private startTime: number = 0
  private visibilityObserver: IntersectionObserver | null = null
  
  constructor(
    private tutorialId: number,
    private sections: ChapterSection[],
    private onProgressUpdate: (progress: LearningProgress) => void,
    private updateFrequency: number = 3000 // 3秒更新一次
  ) {}

  start() {
    this.startTime = Date.now()
    
    // 开始定期更新
    this.updateInterval = setInterval(() => {
      this.updateProgress()
    }, this.updateFrequency)

    // 监听滚动事件
    this.setupScrollListener()
    
    // 监听章节可见性变化
    this.setupVisibilityListener()
    
    console.log('📊 学习进度跟踪器已启动')
  }

  stop() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
    
    if (this.visibilityObserver) {
      this.visibilityObserver.disconnect()
      this.visibilityObserver = null
    }
    
    // 最后一次更新进度
    this.updateProgress()
    
    console.log('📊 学习进度跟踪器已停止')
  }

  private setupScrollListener() {
    let ticking = false
    const updateScrollProgress = () => {
      const scrollTop = window.pageYOffset
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollProgress = docHeight > 0 ? Math.min((scrollTop / docHeight) * 100, 100) : 0
      
      this.lastScrollPosition = scrollProgress
      ticking = false
    }

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollProgress)
        ticking = true
      }
    }, { passive: true })
  }

  private setupVisibilityListener() {
    this.visibilityObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const sectionId = entry.target.getAttribute('data-section') || 
                           entry.target.getAttribute('data-subsection') ||
                           entry.target.id

          if (sectionId) {
            if (entry.isIntersecting) {
              // 章节进入视野，开始计时
              this.sectionTimers[sectionId] = Date.now()
              this.currentSection = sectionId
            } else if (this.sectionTimers[sectionId]) {
              // 章节离开视野，累加观看时间
              const timeSpent = (Date.now() - this.sectionTimers[sectionId]) / 1000
              this.addSectionTime(sectionId, timeSpent)
              delete this.sectionTimers[sectionId]
            }
          }
        })
      },
      {
        threshold: 0.3, // 30%可见即算观看
        rootMargin: '-50px 0px -50px 0px'
      }
    )

    // 观察所有章节
    this.sections.forEach(section => {
      if (section.element) {
        this.visibilityObserver?.observe(section.element)
      }
      section.subsections?.forEach(sub => {
        if (sub.element) {
          this.visibilityObserver?.observe(sub.element)
        }
      })
    })
  }

  private addSectionTime(sectionId: string, timeSpent: number) {
    const currentProgress = LearningProgressStore.load(this.tutorialId)
    const timeInView = { ...currentProgress.timeInView }
    
    // 累加观看时间
    timeInView[sectionId] = (timeInView[sectionId] || 0) + timeSpent

    // 检查是否自动完成章节
    const section = this.findSection(sectionId)
    if (section && !currentProgress.completedSections.includes(sectionId)) {
      if (detectSectionCompletion(
        sectionId, 
        timeInView[sectionId], 
        this.lastScrollPosition, 
        section.estimatedTime
      )) {
        const newCompletedSections = [...currentProgress.completedSections, sectionId]
        console.log(`✅ 自动完成章节: ${section.title}`)
        
        // 触发完成事件，更新UI
        this.onSectionAutoCompleted(sectionId, section.title)
        
        // 更新进度
        const updatedProgress = {
          ...currentProgress,
          completedSections: newCompletedSections,
          timeInView,
          currentSection: this.currentSection
        }
        
        LearningProgressStore.save(this.tutorialId, updatedProgress)
        this.onProgressUpdate(updatedProgress)
        return
      }
    }

    // 普通更新
    const updatedProgress = {
      ...currentProgress,
      timeInView,
      currentSection: this.currentSection
    }
    
    LearningProgressStore.save(this.tutorialId, updatedProgress)
  }

  private findSection(sectionId: string): ChapterSection | null {
    for (const section of this.sections) {
      if (section.id === sectionId) return section
      if (section.subsections) {
        for (const sub of section.subsections) {
          if (sub.id === sectionId) return sub
        }
      }
    }
    return null
  }

  private onSectionAutoCompleted(sectionId: string, title: string) {
    // 发送自定义事件，通知UI更新
    window.dispatchEvent(new CustomEvent('sectionAutoCompleted', {
      detail: { sectionId, title }
    }))
  }

  private updateProgress() {
    const currentProgress = LearningProgressStore.load(this.tutorialId)
    
    // 更新当前章节的观看时间
    if (this.currentSection && this.sectionTimers[this.currentSection]) {
      const timeSpent = (Date.now() - this.sectionTimers[this.currentSection]) / 1000
      this.addSectionTime(this.currentSection, timeSpent)
      this.sectionTimers[this.currentSection] = Date.now() // 重置计时器
    }

    // 计算总学习时间（从开始学习到现在）
    const sessionTime = Math.floor((Date.now() - this.startTime) / 60000) // 转换为分钟
    const totalTimeSpent = currentProgress.totalTimeSpent + sessionTime

    // 使用混合进度计算
    const updatedProgress: LearningProgress = {
      ...currentProgress,
      scrollProgress: this.lastScrollPosition,
      totalTimeSpent: sessionTime, // 只计算本次会话时间
      currentSection: this.currentSection,
      lastAccessed: new Date().toISOString()
    }

    // 计算综合进度
    const advancedProgress = calculateAdvancedProgress(this.sections, updatedProgress)
    updatedProgress.progressPercentage = advancedProgress

    // 保存并通知更新
    LearningProgressStore.save(this.tutorialId, updatedProgress)
    this.onProgressUpdate(updatedProgress)
  }

  // 手动标记章节完成
  markSectionComplete(sectionId: string) {
    const currentProgress = LearningProgressStore.load(this.tutorialId)
    if (!currentProgress.completedSections.includes(sectionId)) {
      const newCompletedSections = [...currentProgress.completedSections, sectionId]
      const updatedProgress = {
        ...currentProgress,
        completedSections: newCompletedSections
      }
      
      LearningProgressStore.save(this.tutorialId, updatedProgress)
      this.onProgressUpdate(updatedProgress)
    }
  }

  // 更新章节元素引用（当DOM重新渲染时调用）
  updateSectionElements(sections: ChapterSection[]) {
    this.sections = sections
    
    // 重新设置观察器
    if (this.visibilityObserver) {
      this.visibilityObserver.disconnect()
      this.setupVisibilityListener()
    }
  }

  // 获取当前状态统计
  getStats() {
    const currentProgress = LearningProgressStore.load(this.tutorialId)
    const totalSections = this.sections.reduce((count, section) => 
      count + 1 + (section.subsections?.length || 0), 0)
    
    return {
      currentSection: this.currentSection,
      scrollProgress: this.lastScrollPosition,
      completedSections: currentProgress.completedSections.length,
      totalSections,
      progressPercentage: currentProgress.progressPercentage,
      totalTimeSpent: currentProgress.totalTimeSpent,
      sessionTime: Math.floor((Date.now() - this.startTime) / 60000)
    }
  }
}