"use client"

import { useState, useEffect, useRef } from "react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, BookOpen, Lock, Unlock, Clock, Tag, Trophy, Target, MapPin } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { TableOfContents } from "@/components/learning/TableOfContents"
import { 
  parseContentSections,
  LearningProgressStore,
  type ChapterSection
} from "@/lib/learning-utils"
import { OptimizedScrollTracker, type OptimizedScrollConfig, type SimpleProgress } from "@/lib/optimized-scroll-tracker"

interface Tutorial {
  id: number
  title: string
  description: string
  content: string
  category_name: string
  tags: string[]
  price: number
  status: string
  created_at: string
}

export default function TutorialPage() {
  const params = useParams()
  const router = useRouter()
  const [tutorial, setTutorial] = useState<Tutorial | null>(null)
  const [isUnlocked, setIsUnlocked] = useState(false)
  const [loading, setLoading] = useState(true)
  const [sections, setSections] = useState<ChapterSection[]>([])
  const [currentSection, setCurrentSection] = useState<string>('')
  const [learningProgress, setLearningProgress] = useState<SimpleProgress | null>(null)
  const [isLearning, setIsLearning] = useState(false)
  const learningTrackerRef = useRef<OptimizedScrollTracker | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    if (params.id) {
      loadTutorial(params.id as string)
    }
  }, [params.id])

  // 初始化高性能滚动跟踪器
  useEffect(() => {
    if (tutorial && isUnlocked) {
      const parsedSections = parseContentSections(tutorial.content)
      setSections(parsedSections)
      
      // 加载本地学习进度（简化版）
      const defaultProgress: SimpleProgress = {
        tutorialId: tutorial.id,
        progressPercentage: 0,
        scrollProgress: 0,
        currentSection: '',
        lastAccessed: new Date().toISOString()
      }
      
      try {
        const stored = localStorage.getItem(`scroll_progress_${tutorial.id}`)
        const savedProgress = stored ? { ...defaultProgress, ...JSON.parse(stored) } : defaultProgress
        setLearningProgress(savedProgress)
        setCurrentSection(savedProgress.currentSection)
      } catch (error) {
        console.warn('加载进度失败:', error)
        setLearningProgress(defaultProgress)
      }
      
      // 创建高性能滚动跟踪器
      const trackerConfig: OptimizedScrollConfig = {
        tutorialId: tutorial.id,
        sections: parsedSections,
        updateThrottle: 100, // 100ms实时更新
        onProgressUpdate: (progress) => {
          console.log('📊 滚动进度更新:', progress.progressPercentage + '%')
          setLearningProgress(progress)
          
          // 每1%进度变化就同步到服务器
          updateServerProgress(progress)
        },
        onSectionChange: (sectionId) => {
          console.log('📍 当前章节:', sectionId)
          setCurrentSection(sectionId)
          
          // 移除频繁的章节变化提示，仅在控制台记录
        }
      }
      
      learningTrackerRef.current = new OptimizedScrollTracker(trackerConfig)
      
      // 立即启动，避免延迟跳跃
      learningTrackerRef.current.start()
      setIsLearning(true)
      console.log('🚀 高性能滚动跟踪已启动')
      
      // 清理函数
      return () => {
        if (learningTrackerRef.current) {
          learningTrackerRef.current.destroy()
          learningTrackerRef.current = null
        }
      }
    }
  }, [tutorial, isUnlocked])

  const handleSectionClick = (sectionId: string) => {
    // 使用跟踪器设置当前章节
    if (learningTrackerRef.current) {
      learningTrackerRef.current.setCurrentSection(sectionId)
    }
    
    // 滚动到章节
    const selectors = [
      `[data-section="${sectionId}"]`,
      `[data-subsection="${sectionId}"]`,
      `#${sectionId}`,
      `#section-${sectionId}`
    ]
    
    for (const selector of selectors) {
      const element = document.querySelector(selector)
      if (element) {
        const top = element.getBoundingClientRect().top + window.pageYOffset - 120
        window.scrollTo({
          top: top,
          behavior: 'smooth'
        })
        break
      }
    }
  }

  const markSectionComplete = (sectionId: string) => {
    // 简化版本不需要手动标记，仅用于兼容
    console.log('标记章节完成:', sectionId)
  }

  const updateServerProgress = async (progressData: SimpleProgress) => {
    if (!tutorial) return
    
    try {
      console.log('🚀 同步滚动进度到服务器:', progressData)
      
      const response = await fetch('/api/learning/progress', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        },
        body: JSON.stringify({
          tutorialId: tutorial.id,
          sectionId: null, // 简化版本不再发送章节ID
          status: progressData.progressPercentage === 100 ? 'completed' : 'in_progress',
          progressPercentage: progressData.progressPercentage,
          timeSpent: 0, // 移除时间跟踪
          interactionData: {
            scrollPercentage: progressData.scrollProgress,
            currentSection: progressData.currentSection,
            deviceType: 'web'
          }
        })
      })

      const result = await response.json()
      console.log('📊 服务器响应:', result)

      if (!response.ok || !result.success) {
        throw new Error(result.error || `HTTP ${response.status}`)
      }

      console.log('✅ 滚动进度同步成功')
      
      // 仅在学习完成时显示成就提示
      if (progressData.progressPercentage === 100 && result.achievements && result.achievements.length > 0) {
        result.achievements.forEach((achievement: any) => {
          toast({
            title: "🏆 学习完成！成就解锁",
            description: `${achievement.display_name}: ${achievement.description}`,
            duration: 8000 // 完成提示显示更久
          })
        })
      }

    } catch (error) {
      console.error('❌ 进度同步失败:', error)
      toast({
        title: "同步失败",
        description: "学习进度已本地保存，将在下次连接时同步",
        variant: "destructive"
      })
    }
  }

  const loadTutorial = async (id: string) => {
    try {
      const response = await fetch(`/api/tutorial/${id}`)

      if (response.ok) {
        const data = await response.json()
        setTutorial(data.tutorial)
        setIsUnlocked(data.is_unlocked)
      } else if (response.status === 404) {
        toast({
          title: "教程不存在",
          description: "请检查链接是否正确",
          variant: "destructive",
        })
        router.push("/")
      } else if (response.status === 403) {
        toast({
          title: "需要解锁",
          description: "请使用有效密钥解锁此教程",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "加载失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!tutorial) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="text-center py-8">
            <BookOpen className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold mb-2">教程不存在</h2>
            <p className="text-gray-600 mb-4">请检查链接是否正确</p>
            <Button onClick={() => router.push("/")}>返回首页</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const totalEstimatedTime = sections.reduce((total, section) => 
    total + section.estimatedTime + (section.subsections?.reduce((subTotal, sub) => subTotal + sub.estimatedTime, 0) || 0), 0
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.push("/")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回首页
            </Button>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h1 className="text-xl font-semibold">{tutorial.title}</h1>
                {tutorial.status === 'archived' && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200">
                    已下架
                  </Badge>
                )}
              </div>
              <p className="text-gray-600 text-sm">{tutorial.category_name}</p>
            </div>
            {isUnlocked && learningProgress && (
              <div className="flex items-center space-x-4">
                <Badge variant="outline">
                  进度：{learningProgress.progressPercentage}%
                </Badge>
                <Badge variant={isLearning ? "default" : "secondary"}>
                  {isLearning ? "⚡ 高性能跟踪" : "⏸️ 跟踪暂停"}
                </Badge>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative">
        {isUnlocked ? (
          <>
            {/* 移动端目录 */}
            <div className="lg:hidden">
              <div className="container mx-auto px-4 py-6">
                <TableOfContents
                  chapters={sections}
                  currentSection={currentSection}
                  completedSections={[]}
                  totalProgress={learningProgress?.progressPercentage || 0}
                  totalTimeSpent={0}
                  onSectionClick={handleSectionClick}
                  onMarkComplete={markSectionComplete}
                />
              </div>
            </div>
            
            {/* 桌面端并列布局 */}
            <div className="hidden lg:block">
              <div className="container mx-auto px-4 py-4">
                <div className="flex gap-6">
                  {/* 左侧目录 - 贴近页面边缘 */}
                  <div className="w-80 flex-shrink-0">
                    <div className="fixed left-0 top-[5rem] bottom-0 z-30">
                      <div className="w-72 h-full">
                        <TableOfContents
                          chapters={sections}
                          currentSection={currentSection}
                          completedSections={[]}
                          totalProgress={learningProgress?.progressPercentage || 0}
                          totalTimeSpent={0}
                          onSectionClick={handleSectionClick}
                          onMarkComplete={markSectionComplete}
                          className="h-full"
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* 右侧内容区域 */}
                  <div className="flex-1 max-w-4xl">
              {/* 教程总结栏目 */}
              <Card className="mb-6">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-2xl mb-3 flex items-center">
                        <BookOpen className="h-6 w-6 mr-3" />
                        {tutorial.title}
                        {tutorial.status === 'archived' && (
                          <Badge variant="outline" className="ml-3 bg-orange-50 text-orange-600 border-orange-200">
                            已下架
                          </Badge>
                        )}
                      </CardTitle>
                      {tutorial.status === 'archived' && (
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                          <p className="text-orange-700 text-sm">
                            <span className="font-medium">注意：</span>
                            此教程已下架，不再对外销售。但您已经解锁，可以继续学习内容。
                          </p>
                        </div>
                      )}
                      <CardDescription className="text-base mb-4">
                        {tutorial.description}
                      </CardDescription>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-blue-600" />
                          <span className="text-sm">
                            预计时长：{totalEstimatedTime} 分钟
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Target className="h-4 w-4 text-green-600" />
                          <span className="text-sm">
                            {sections.length} 个章节
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Trophy className="h-4 w-4 text-orange-600" />
                          <span className="text-sm">
                            互动学习
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 mb-4">
                        <Badge variant="secondary">{tutorial.category_name}</Badge>
                        <Badge className="bg-green-100 text-green-800">
                          <Unlock className="h-3 w-3 mr-1" />
                          已解锁
                        </Badge>
                      </div>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {tutorial.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      {learningProgress && (
                        <div className="space-y-3 pt-4 border-t">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">学习进度</span>
                            <span className="text-sm text-gray-500">
                              {learningProgress.progressPercentage}%
                            </span>
                          </div>
                          <Progress value={learningProgress.progressPercentage} className="h-2" />
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>
                              滚动进度：{learningProgress.progressPercentage}%
                            </span>
                            <span>
                              当前章节：{learningProgress.currentSection || '未开始'}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardHeader>
              </Card>

              {/* 教程内容 */}
              <Card>
                <CardContent className="p-6">
                  <ErrorBoundary
                    onError={(error) => {
                      console.error('教程内容渲染错误:', error)
                      toast({
                        title: "内容渲染错误",
                        description: "教程内容格式可能有问题，请联系管理员",
                        variant: "destructive"
                      })
                    }}
                  >
                    {tutorial.content ? (
                      <div
                        className="tutorial-content prose max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100"
                        style={{
                          lineHeight: '1.6'
                        }}
                        dangerouslySetInnerHTML={{ __html: tutorial.content }}
                      />
                    ) : (
                      <div className="text-center py-12 text-gray-500">
                        <p>该教程暂无内容</p>
                      </div>
                    )}
                  </ErrorBoundary>
                </CardContent>
              </Card>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="container mx-auto px-4 py-6">
            <Card className="border-2 border-dashed border-gray-300 max-w-2xl mx-auto">
              <CardContent className="text-center py-12">
                <Lock className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold mb-2">内容已锁定</h3>
                <p className="text-gray-600 mb-6">请使用有效的验证密钥解锁此教程内容</p>
                <Button onClick={() => router.push("/")}>前往解锁</Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
