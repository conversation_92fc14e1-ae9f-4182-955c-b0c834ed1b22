import { NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

export async function POST() {
  try {
    console.log('🔧 开始数据一致性修复...')

    // 1. 找出所有孤立的解锁记录（对应密钥不存在或状态非used）
    const { data: allUnlocks, error: unlocksError } = await supabaseAdmin
      .from('user_unlocks')
      .select(`
        id,
        key_id,
        tutorial_id,
        user_identifier,
        tutorials(title)
      `)

    if (unlocksError) throw unlocksError

    console.log(`📊 总解锁记录数: ${allUnlocks?.length || 0}`)

    const orphanedUnlocks = []
    
    for (const unlock of allUnlocks || []) {
      // 检查对应的密钥是否存在且状态为used
      const { data: keyData, error: keyError } = await supabaseAdmin
        .from('tutorial_keys')
        .select('id, status, key_code')
        .eq('id', unlock.key_id)
        .single()

      if (keyError || !keyData || keyData.status !== 'used') {
        orphanedUnlocks.push({
          unlock_id: unlock.id,
          key_id: unlock.key_id,
          tutorial_title: unlock.tutorials?.title || '未知',
          key_status: keyData?.status || '已删除',
          user: unlock.user_identifier
        })
      }
    }

    console.log(`❌ 发现孤立解锁记录: ${orphanedUnlocks.length}`)

    // 2. 找出所有缺少解锁记录的已使用密钥
    const { data: allUsedKeys, error: usedKeysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select(`
        id,
        key_code,
        tutorial_id,
        user_identifier,
        used_at,
        tutorials(title)
      `)
      .eq('status', 'used')

    if (usedKeysError) throw usedKeysError

    console.log(`📊 已使用密钥数: ${allUsedKeys?.length || 0}`)

    const keysWithoutUnlocks = []

    for (const key of allUsedKeys || []) {
      // 检查是否有对应的解锁记录
      const { data: unlockData, error: unlockError } = await supabaseAdmin
        .from('user_unlocks')
        .select('id')
        .eq('key_id', key.id)
        .single()

      if (unlockError || !unlockData) {
        keysWithoutUnlocks.push({
          key_id: key.id,
          key_code: key.key_code,
          tutorial_title: key.tutorials?.title || '未知',
          user_identifier: key.user_identifier,
          used_at: key.used_at
        })
      }
    }

    console.log(`❌ 发现缺少解锁记录的已使用密钥: ${keysWithoutUnlocks.length}`)

    // 3. 执行修复操作
    let fixedCount = 0

    // 删除孤立的解锁记录
    if (orphanedUnlocks.length > 0) {
      console.log(`🧹 删除${orphanedUnlocks.length}条孤立解锁记录...`)
      
      const orphanIds = orphanedUnlocks.map(o => o.unlock_id)
      const { error: deleteError } = await supabaseAdmin
        .from('user_unlocks')
        .delete()
        .in('id', orphanIds)

      if (deleteError) {
        console.error('删除孤立记录失败:', deleteError)
      } else {
        fixedCount += orphanedUnlocks.length
        console.log(`✅ 成功删除${orphanedUnlocks.length}条孤立解锁记录`)
      }
    }

    // 为缺少解锁记录的已使用密钥创建解锁记录
    if (keysWithoutUnlocks.length > 0) {
      console.log(`🔧 为${keysWithoutUnlocks.length}个已使用密钥创建解锁记录...`)
      
      const unlockRecords = keysWithoutUnlocks.map(key => ({
        user_identifier: key.user_identifier || 'system-repair',
        tutorial_id: key.tutorial_id,
        key_id: key.key_id,
        ip_address: 'data-repair',
        user_agent: 'data-repair-script',
        unlocked_at: key.used_at || new Date().toISOString()
      }))

      const { error: insertError } = await supabaseAdmin
        .from('user_unlocks')
        .insert(unlockRecords)

      if (insertError) {
        console.error('创建解锁记录失败:', insertError)
      } else {
        fixedCount += keysWithoutUnlocks.length
        console.log(`✅ 成功创建${keysWithoutUnlocks.length}条解锁记录`)
      }
    }

    // 4. 重新检查统计数据
    const { count: newUnlocks } = await supabaseAdmin
      .from('user_unlocks')
      .select('id', { count: 'exact', head: true })

    const { count: newUsedKeys } = await supabaseAdmin
      .from('tutorial_keys')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'used')

    console.log(`📊 修复后统计:`)
    console.log(`  总解锁次数: ${newUnlocks}`)
    console.log(`  已使用密钥: ${newUsedKeys}`)
    console.log(`  差值: ${(newUnlocks || 0) - (newUsedKeys || 0)}`)

    return NextResponse.json({
      success: true,
      message: "数据一致性修复完成",
      details: {
        orphaned_unlocks_removed: orphanedUnlocks.length,
        missing_unlocks_created: keysWithoutUnlocks.length,
        total_fixes: fixedCount,
        before: {
          unlocks: allUnlocks?.length || 0,
          used_keys: allUsedKeys?.length || 0
        },
        after: {
          unlocks: newUnlocks || 0,
          used_keys: newUsedKeys || 0
        },
        orphaned_details: orphanedUnlocks,
        missing_details: keysWithoutUnlocks
      }
    })

  } catch (error) {
    console.error("❌ 数据修复失败:", error)
    return NextResponse.json({ 
      error: "数据修复失败",
      details: error instanceof Error ? error.message : "未知错误"
    }, { status: 500 })
  }
}