/**
 * 简化的数据一致性检查工具
 * 通过SQL直接查询数据库比较解锁记录和已使用密钥
 */

async function checkDataConsistency() {
  try {
    console.log('🔍 检查数据一致性...\n')

    // 通过API获取统计数据
    const response = await fetch('http://localhost:3002/api/admin/stats')
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }
    
    const stats = await response.json()
    console.log('📊 当前统计数据:')
    console.log(`  总解锁次数: ${stats.total_unlocks}`)
    console.log(`  已兑换密钥: ${stats.used_keys}`)
    console.log(`  差值: ${stats.total_unlocks - stats.used_keys}\n`)

    // 分析可能的原因
    console.log('🔍 可能的原因分析:')
    
    if (stats.total_unlocks > stats.used_keys) {
      console.log('  1. ❌ 存在孤立的解锁记录（对应密钥已删除）')
      console.log('  2. ❌ 存在状态非"used"的密钥但有解锁记录')
      console.log('  3. ❌ 数据删除操作不一致')
    } else if (stats.total_unlocks < stats.used_keys) {
      console.log('  1. ❌ 存在"used"状态密钥但没有解锁记录')
      console.log('  2. ❌ 解锁记录创建失败但密钥状态已更新')
    } else {
      console.log('  ✅ 数据一致性良好')
      return
    }

    console.log('\n💡 修复建议:')
    console.log('  1. 运行清理脚本删除孤立的解锁记录')
    console.log('  2. 为无解锁记录的已使用密钥创建对应记录')
    console.log('  3. 检查删除密钥时的级联删除逻辑')

  } catch (error) {
    console.error('❌ 检查失败:', error)
  }
}

// 检查函数
checkDataConsistency()