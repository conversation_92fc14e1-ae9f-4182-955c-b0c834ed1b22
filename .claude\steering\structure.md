# 项目结构文档 - 知识商城

## 项目根目录结构（当前状态）

```
F:\编程项目\知识商城\
├── .claude/                    # Claude Code 配置（新增）
│   └── steering/              # 项目指导文档
│       ├── product.md         # 产品规划文档
│       ├── tech.md            # 技术架构文档
│       └── structure.md       # 本文档
├── app/                       # Next.js App Router 目录
├── components/                # React 组件库（大幅扩展）
├── content/                   # 静态内容文件（新增）
├── docs/                      # 项目文档（完善）
├── hooks/                     # 自定义 React Hooks
├── lib/                       # 工具函数和配置（扩展）
├── node_modules/              # 依赖包 (自动生成)
├── public/                    # 静态资源
├── scripts/                   # 数据库和工具脚本（17个脚本）
├── styles/                    # 全局样式文件
├── CLAUDE.md                  # Claude Code 项目配置
├── package.json               # 项目依赖配置（69个依赖）
├── tsconfig.json              # TypeScript 配置
├── tailwind.config.ts         # Tailwind CSS 配置
└── next.config.mjs            # Next.js 配置
```

## 应用目录结构 (app/) - 大幅扩展

### 页面路由组织
```
app/
├── page.tsx                   # 首页 (/)
├── layout.tsx                 # 根布局组件
├── loading.tsx                # 全局加载状态
├── globals.css                # 全局样式
├── admin/                     # 管理后台
│   ├── page.tsx              # 管理首页 (/admin)
│   └── create-tutorial/      # 创建教程
│       └── page.tsx
├── api/                      # API 路由（大幅扩展）
├── my-tutorials/             # 用户教程页面
│   ├── page.tsx
│   └── loading.tsx
├── tutorial/[id]/            # 教程详情页面（重构）
│   └── page.tsx              # 支持智能进度跟踪
└── wechat/                   # 微信相关页面
    └── callback/
        └── page.tsx
```

### API 路由结构（完整实现 + 最新优化）
```
api/
├── admin/                    # 管理员 API（需要认证）
│   ├── auth/                # 管理员认证
│   │   └── route.ts
│   ├── categories/          # 分类管理
│   │   └── route.ts
│   ├── keys/                # 密钥管理（增强 + 缓存优化）
│   │   ├── route.ts         # 无缓存强制刷新
│   │   ├── generate/
│   │   │   └── route.ts
│   │   ├── delete/          # 密钥删除API（新增）
│   │   │   └── route.ts
│   │   └── [tutorialId]/
│   │       ├── route.ts
│   │       ├── clear/
│   │       │   └── route.ts
│   │       └── export/
│   │           └── route.ts
│   ├── media/               # 媒体文件管理（新增）
│   │   └── route.ts
│   ├── stats/               # 统计数据
│   │   └── route.ts
│   ├── tutorials/           # 教程管理
│   │   ├── route.ts
│   │   └── [id]/
│   │       ├── route.ts
│   │       ├── archive/
│   │       │   └── route.ts
│   │       └── publish/
│   │           └── route.ts
│   └── announcements/       # 公告管理（新增）
│       ├── route.ts
│       └── [id]/
│           └── route.ts
├── learning/                # 学习功能（全新模块）
│   └── progress/            # 进度跟踪API
│       └── route.ts
├── public/                  # 公开 API（缓存优化）
│   ├── categories/          # 公开分类列表
│   │   └── route.ts
│   └── tutorials/           # 公开教程列表（无缓存策略）
│       └── route.ts
├── revalidate/              # 缓存失效API（新增）
│   └── route.ts
├── health/                  # 健康检查API（新增）
│   └── route.ts
├── announcements/           # 公告获取（新增）
│   └── route.ts
├── tutorial/[id]/           # 单个教程获取
│   └── route.ts
├── user-unlocks/            # 用户解锁记录（缓存优化）
│   └── route.ts
├── verify-key/              # 密钥验证（实时更新优化）
│   └── route.ts
└── wechat/                  # 微信集成
    ├── auth/
    │   └── route.ts
    └── login/
        └── route.ts
```

## 组件库结构 (components/) - 重大扩展

### 组件分层架构（当前47个组件）
```
components/
├── ui/                      # 基础 UI 组件 (shadcn/ui) - 38个组件
│   ├── accordion.tsx        # 手风琴组件
│   ├── alert.tsx           # 警告组件
│   ├── alert-dialog.tsx    # 警告对话框
│   ├── avatar.tsx          # 头像组件
│   ├── badge.tsx           # 徽章组件
│   ├── button.tsx          # 按钮组件
│   ├── card.tsx            # 卡片组件
│   ├── checkbox.tsx        # 复选框组件
│   ├── dialog.tsx          # 对话框组件
│   ├── dropdown-menu.tsx   # 下拉菜单
│   ├── form.tsx            # 表单组件
│   ├── input.tsx           # 输入组件
│   ├── label.tsx           # 标签组件
│   ├── progress.tsx        # 进度条组件（学习进度核心）
│   ├── select.tsx          # 选择器组件
│   ├── separator.tsx       # 分隔符组件
│   ├── table.tsx           # 表格组件
│   ├── tabs.tsx            # 标签页组件
│   ├── toast.tsx           # 提示组件
│   └── ...                 # 其他基础组件
├── admin/                  # 管理员专用组件
│   ├── MediaManager.tsx    # 媒体管理器
│   └── TutorialKeysManager.tsx # 密钥管理器
├── editor/                 # 编辑器相关组件（重大升级）
│   ├── TutorialEditor.tsx  # 普通编辑器
│   ├── StructuredTutorialEditor.tsx # 结构化编辑器（新增）
│   └── TutorialEditor.tsx.backup    # 备份文件
├── learning/               # 学习功能组件（全新模块）
│   ├── LearningProgress.tsx # 学习进度组件
│   └── TableOfContents.tsx  # 章节目录组件（新增核心）
├── AnnouncementBell.tsx    # 公告铃铛组件（新增）
├── theme-provider.tsx      # 主题提供者
└── wechat-login-dialog.tsx # 微信登录对话框
```

### 核心学习组件详解（新增重点）

#### TableOfContents.tsx（章节目录导航）
- **功能**：左侧可折叠章节导航
- **特性**：
  - 实时进度显示和完成状态
  - 章节类型标识（介绍/章节/检查点/互动/总结）
  - 一键导航到指定章节
  - 预计学习时间显示
  - 响应式折叠设计

#### LearningProgress.tsx（学习进度跟踪）
- **功能**：学习进度数据管理和可视化
- **特性**：
  - 实时进度计算和更新
  - 学习时长统计
  - 成就系统集成
  - 跨会话数据持久化

#### StructuredTutorialEditor.tsx（结构化编辑器）
- **功能**：支持进度跟踪的内容编辑器
- **特性**：
  - 可视化章节属性添加
  - 检查点和互动练习插入
  - 实时预览和验证
  - 与普通编辑器完全兼容

## 自定义Hooks (hooks/) - 核心数据管理

### 数据管理Hooks（优化版）
```
hooks/
├── use-simple-refresh.ts        # 简单刷新钩子（缓存优化）
├── use-tutorials-simple.ts      # 教程数据钩子（无缓存策略）
├── use-admin-auth.ts            # 管理员认证钩子
├── use-learning-progress.ts     # 学习进度钩子（新增）
├── use-announcement.ts          # 公告数据钩子（新增）
└── use-media-manager.ts         # 媒体管理钩子（新增）
```

### 核心Hook功能详解

#### use-simple-refresh.ts（缓存优化版）
```typescript
// 特性：强制无缓存刷新机制
const { data, loading, error, refresh } = useSimpleRefresh({
  url: '/api/public/tutorials',
  forceNoCache: true,  // 新增：强制无缓存
  dependencies: []
})

// 实现：
fetch(requestUrl, {
  headers: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }
})
```

#### use-tutorials-simple.ts（实时更新版）
```typescript
// 特性：支持实时数据更新，无缓存策略
const { tutorials, userUnlocks, loading, refresh } = useTutorialsSimple({
  autoRefresh: true,        // 自动刷新
  noCache: true,           // 无缓存模式
  onDataUpdate: callback   // 数据更新回调
})
```

### 核心工具模块（当前8个文件）
```
lib/
├── auth.ts                 # 认证相关工具
├── database.ts             # 数据库操作工具
├── key-generator.ts        # 密钥生成工具
├── supabase.ts             # Supabase 客户端配置
├── utils.ts                # 通用工具函数
├── test-connection.ts      # 数据库连接测试
├── learning-utils.ts       # 学习功能核心（新增重点）
└── 其他工具文件
```

### 学习功能核心 (learning-utils.ts)（新增重点）
```typescript
// 核心接口定义
interface ChapterSection {
  id: string
  title: string
  estimatedTime: number
  type: 'intro' | 'chapter' | 'checkpoint' | 'interactive' | 'conclusion'
  completed: boolean
  currentlyViewing: boolean
  subsections?: ChapterSection[]
  element?: Element
}

interface LearningProgress {
  tutorialId: number
  completedSections: string[]
  currentSection: string
  totalTimeSpent: number
  progressPercentage: number
  lastAccessed: string
  sectionTimeSpent: Record<string, number>
}

// 核心功能函数
- parseContentSections()     # 智能内容解析
- calculateProgressPercentage() # 进度百分比计算
- setupSectionObserver()     # 滚动监听设置
- scrollToSection()          # 章节导航
- formatLearningTime()       # 时间格式化
- LearningProgressStore      # 本地存储管理类
```

## 脚本目录 (scripts/) - 完整的数据库管理

### 数据库脚本（17个文件）
```
scripts/
├── 01-create-tables.sql          # 创建核心数据表
├── 02-seed-data.sql              # 种子数据
├── 03-setup-rls.sql              # 行级安全策略
├── 04-create-admin-password.sql  # 管理员密码
├── 05-add-test-keys.sql          # 测试密钥
├── 06-content-management-tables.sql # 内容管理表
├── 06-tutorial-media-table.sql   # 媒体资源表
├── 07-storage-configuration.sql  # 存储配置
├── 07-update-keys-table.sql      # 密钥表更新
├── 08-create-announcements-table.sql # 公告系统表
├── 08-learning-progress-tables.sql   # 学习进度表（新增）
├── 09-update-uiux-tutorial-content.sql # 示例内容
├── update-tutorial-status.sql    # 教程状态更新
└── *.js                          # JavaScript 工具脚本
```

### JavaScript 工具脚本
```
scripts/
├── add-test-keys.js           # 测试密钥生成
├── check-database-status.js   # 数据库状态检查
├── check-keys.js              # 密钥验证测试
├── generate-admin-hash.js     # 管理员密码哈希
├── setup-content-management.js # 内容管理设置
├── test-media-setup.js        # 媒体功能测试
├── test-website-function.js   # 网站功能测试
└── verify-runtime-fix.js      # 运行时修复验证
```

## 文档目录 (docs/) - 完善的文档体系

### 文档分类（当前状态）
```
docs/
├── README.md                          # 项目总览
├── development-plan.md                # 开发计划
├── task-checklist.md                  # 任务清单（实时更新）
├── technical-standards.md             # 技术标准
├── supabase-migration.md              # 数据库迁移指南
├── wechat-login-setup.md              # 微信登录配置
├── tutorial-content-architecture.md   # 教程内容架构
├── tutorial-content-format.md         # 教程格式规范
├── learning-progress-system-guide.md  # 学习系统指南（新增）
├── learning-progress-model.md         # 学习模型设计（新增）
├── key-management-system.md           # 密钥管理系统
├── announcement-*.md                  # 公告系统文档
└── *.md                               # 其他文档
```

## 命名约定（标准化）

### 文件命名规则
- **页面文件**：`page.tsx`, `layout.tsx`, `loading.tsx`
- **组件文件**：PascalCase (如 `TableOfContents.tsx`)
- **工具文件**：kebab-case (如 `learning-utils.ts`)
- **API 路由**：`route.ts`
- **配置文件**：小写+扩展名 (如 `next.config.mjs`)

### 目录命名规则
- **功能目录**：kebab-case (如 `create-tutorial/`)
- **组件目录**：小写 (如 `ui/`, `admin/`, `learning/`)
- **API 目录**：kebab-case (如 `user-unlocks/`)

### 组件命名规范
- **页面组件**：功能描述 + Page (如 `TutorialPage`)
- **UI组件**：功能描述 (如 `TableOfContents`)
- **布局组件**：功能描述 + Layout (如 `AdminLayout`)
- **学习组件**：Learning + 功能 (如 `LearningProgress`)

## 代码组织模式（升级版）

### 导入顺序规范
```typescript
// 1. React 和 Next.js 核心导入
import { useState, useEffect, useRef } from "react"
import { NextRequest, NextResponse } from "next/server"

// 2. 第三方库导入
import { supabaseAdmin } from "@/lib/supabase"

// 3. UI组件导入
import { Button } from "@/components/ui/button"
import { TableOfContents } from "@/components/learning/TableOfContents"

// 4. 学习功能导入（新增）
import { 
  parseContentSections,
  LearningProgressStore,
  type ChapterSection 
} from "@/lib/learning-utils"

// 5. 工具函数导入
import { generateKey } from "@/lib/key-generator"

// 6. 类型定义导入
import type { Tutorial, Category } from "@/types"
```

### 组件结构模式（学习组件特化）
```typescript
// 1. 类型定义（学习相关）
interface LearningComponentProps {
  tutorialId: number
  sections: ChapterSection[]
  onProgressUpdate?: (progress: LearningProgress) => void
}

// 2. 主要组件函数
export default function LearningComponent({ props }: LearningComponentProps) {
  // 3. 学习状态声明
  const [learningProgress, setLearningProgress] = useState<LearningProgress>()
  const [currentSection, setCurrentSection] = useState<string>('')
  const observerRef = useRef<IntersectionObserver | null>(null)
  
  // 4. 学习功能副作用
  useEffect(() => {
    // 设置滚动监听和进度跟踪
  }, [])
  
  // 5. 学习事件处理函数
  const handleSectionChange = (sectionId: string) => {
    // 学习进度更新逻辑
  }
  
  // 6. 渲染返回
  return (
    // JSX with learning features
  )
}

// 7. 学习相关子组件
function LearningSubComponent() {
  // 子组件实现
}
```

## 环境特定配置（当前状态）

### 开发环境
- 热重载启用（Next.js 14）
- 详细错误信息和调试
- 学习进度本地存储（开发调试）
- 开发工具集成（React DevTools）

### 生产环境
- 代码压缩优化（自动）
- 学习数据云端同步（Supabase）
- 错误监控集成（规划中）
- 性能分析启用（内置）

## 扩展性设计（架构级别）

### 模块化原则
- **高内聚，低耦合**：学习模块独立性
- **单一职责原则**：每个组件专注特定功能
- **依赖注入模式**：可配置的学习策略
- **插件化架构**：可扩展的学习功能

### 可维护性考虑
- **清晰的目录层次**：功能模块明确分离
- **统一的命名约定**：一致的开发体验
- **完善的类型定义**：TypeScript严格模式
- **充分的文档说明**：每个模块都有详细文档

### 学习系统扩展点（新增）
- **进度算法**：可插拔的进度计算策略
- **存储策略**：本地+云端混合存储
- **UI主题**：可定制的学习界面
- **分析引擎**：可扩展的学习数据分析

## 性能优化策略（学习功能特化）

### 学习组件优化
- **懒加载**：章节内容按需加载
- **虚拟滚动**：大量章节的性能优化
- **防抖处理**：进度更新频率控制
- **缓存策略**：学习数据智能缓存

### 数据同步优化
- **批量更新**：减少服务器请求频率
- **增量同步**：只同步变更的学习数据
- **离线支持**：网络断开时的本地持久化
- **冲突解决**：多设备学习数据冲突处理

这个结构化文档反映了项目当前的真实状态，特别强调了新增的学习进度跟踪系统和相关的架构改进。