/**
 * 诊断总解锁次数与已兑换数量不匹配问题
 * 深度分析数据表之间的关联关系
 */

const { createClient } = require('@supabase/supabase-js')

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function diagnoseUnlockMismatch() {
  try {
    console.log('🔍 开始诊断解锁次数与已兑换数量不匹配问题...\n')

    // 1. 获取基础统计数据
    console.log('📊 基础统计数据:')
    
    const { count: totalUnlocks, error: unlocksError } = await supabaseAdmin
      .from('user_unlocks')
      .select('id', { count: 'exact', head: true })
    
    if (unlocksError) throw unlocksError
    console.log(`  总解锁次数: ${totalUnlocks}`)

    const { count: usedKeys, error: usedKeysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'used')
    
    if (usedKeysError) throw usedKeysError
    console.log(`  已兑换密钥数量: ${usedKeys}`)
    
    console.log(`  差值: ${totalUnlocks - usedKeys} (${totalUnlocks > usedKeys ? '解锁次数多' : '已兑换多'})\n`)

    // 2. 详细分析所有解锁记录
    console.log('🔍 详细解锁记录分析:')
    const { data: allUnlocks, error: allUnlocksError } = await supabaseAdmin
      .from('user_unlocks')
      .select(`
        id,
        tutorial_id,
        key_id,
        user_identifier,
        created_at,
        tutorials(title, price)
      `)
      .order('created_at', { ascending: false })
    
    if (allUnlocksError) throw allUnlocksError
    
    console.log(`\n📋 所有解锁记录 (${allUnlocks?.length || 0} 条):`)
    allUnlocks?.forEach((unlock, index) => {
      console.log(`  ${index + 1}. ID:${unlock.id} | 密钥ID:${unlock.key_id} | 教程:${unlock.tutorials?.title || '未知'} | 用户:${unlock.user_identifier} | 时间:${unlock.created_at}`)
    })

    // 3. 详细分析所有已使用密钥
    console.log('\n🔑 详细已使用密钥分析:')
    const { data: allUsedKeys, error: allUsedKeysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select(`
        id,
        key_code,
        tutorial_id,
        status,
        created_at,
        tutorials(title, price)
      `)
      .eq('status', 'used')
      .order('created_at', { ascending: false })
    
    if (allUsedKeysError) throw allUsedKeysError
    
    console.log(`\n📋 所有已使用密钥 (${allUsedKeys?.length || 0} 条):`)
    allUsedKeys?.forEach((key, index) => {
      console.log(`  ${index + 1}. ID:${key.id} | 密钥:${key.key_code} | 教程:${key.tutorials?.title || '未知'} | 时间:${key.created_at}`)
    })

    // 4. 交叉验证：检查每个解锁记录对应的密钥状态
    console.log('\n🔄 交叉验证分析:')
    
    const keyIdMap = new Map()
    allUsedKeys?.forEach(key => {
      keyIdMap.set(key.id, key)
    })

    const orphanedUnlocks = []
    const validUnlocks = []

    allUnlocks?.forEach(unlock => {
      if (unlock.key_id && keyIdMap.has(unlock.key_id)) {
        validUnlocks.push(unlock)
      } else {
        orphanedUnlocks.push(unlock)
      }
    })

    console.log(`  ✅ 有效解锁记录: ${validUnlocks.length}`)
    console.log(`  ❌ 孤立解锁记录: ${orphanedUnlocks.length}`)

    if (orphanedUnlocks.length > 0) {
      console.log('\n🚨 发现孤立解锁记录（对应密钥不存在或状态非used）:')
      for (const orphan of orphanedUnlocks) {
        // 查询对应密钥的实际状态
        const { data: keyInfo, error: keyInfoError } = await supabaseAdmin
          .from('tutorial_keys')
          .select('id, key_code, status')
          .eq('id', orphan.key_id)
          .single()
        
        if (keyInfoError || !keyInfo) {
          console.log(`    - 解锁ID:${orphan.id} | 密钥ID:${orphan.key_id} | 状态:密钥已删除 | 教程:${orphan.tutorials?.title}`)
        } else {
          console.log(`    - 解锁ID:${orphan.id} | 密钥ID:${orphan.key_id} | 密钥状态:${keyInfo.status} | 教程:${orphan.tutorials?.title}`)
        }
      }
    }

    // 5. 反向验证：检查每个已使用密钥是否有对应的解锁记录
    console.log('\n🔍 反向验证分析:')
    
    const unlockKeyIdSet = new Set(allUnlocks?.map(unlock => unlock.key_id))
    const keysWithoutUnlocks = []

    allUsedKeys?.forEach(key => {
      if (!unlockKeyIdSet.has(key.id)) {
        keysWithoutUnlocks.push(key)
      }
    })

    console.log(`  ❌ 没有解锁记录的已使用密钥: ${keysWithoutUnlocks.length}`)
    
    if (keysWithoutUnlocks.length > 0) {
      console.log('\n🚨 发现没有解锁记录的已使用密钥:')
      keysWithoutUnlocks.forEach(key => {
        console.log(`    - 密钥ID:${key.id} | 密钥:${key.key_code} | 教程:${key.tutorials?.title}`)
      })
    }

    // 6. 数据修复建议
    console.log('\n💡 数据修复建议:')
    
    if (orphanedUnlocks.length > 0) {
      console.log(`  1. 清理 ${orphanedUnlocks.length} 条孤立解锁记录`)
      console.log(`     这些记录对应的密钥已删除或状态非used，应该删除`)
    }
    
    if (keysWithoutUnlocks.length > 0) {
      console.log(`  2. 为 ${keysWithoutUnlocks.length} 个已使用密钥创建解锁记录`)
      console.log(`     或者将这些密钥状态改为unused`)
    }

    if (orphanedUnlocks.length === 0 && keysWithoutUnlocks.length === 0) {
      console.log(`  ✅ 数据一致性良好，无需修复`)
    }

    // 7. 预期修复后的统计
    const expectedUnlocks = totalUnlocks - orphanedUnlocks.length + keysWithoutUnlocks.length
    console.log(`\n📈 修复后预期统计:`)
    console.log(`  预期总解锁次数: ${expectedUnlocks}`)
    console.log(`  已兑换密钥数量: ${usedKeys}`)
    console.log(`  修复后差值: ${expectedUnlocks - usedKeys}`)

  } catch (error) {
    console.error('❌ 诊断失败:', error)
  }
}

// 运行诊断
diagnoseUnlockMismatch()