#!/usr/bin/env node

/**
 * 测试学习进度表是否正确创建
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function testLearningTables() {
  console.log('🔍 检查学习进度数据表状态...')
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    // 检查表是否存在
    const tables = [
      'tutorial_sections',
      'user_learning_records', 
      'user_learning_stats',
      'learning_achievements',
      'user_achievements',
      'learning_sessions'
    ]

    console.log('\n📋 检查数据表:')
    for (const tableName of tables) {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (error) {
        console.log(`❌ ${tableName}: 不存在或无法访问`)
        console.log(`   错误: ${error.message}`)
      } else {
        console.log(`✅ ${tableName}: 存在且可访问`)
      }
    }

    // 检查函数是否存在
    console.log('\n🔧 检查数据库函数:')
    const { data: functions, error: funcError } = await supabase
      .rpc('update_user_learning_stats', { p_user_identifier: 'test' })
    
    if (funcError) {
      console.log('❌ update_user_learning_stats: 函数不存在或有错误')
      console.log(`   错误: ${funcError.message}`)
    } else {
      console.log('✅ update_user_learning_stats: 函数正常')
    }

    // 检查基础成就数据
    console.log('\n🏆 检查成就数据:')
    const { data: achievements, error: achError } = await supabase
      .from('learning_achievements')
      .select('*')
      .limit(5)
    
    if (achError) {
      console.log('❌ 成就数据查询失败:', achError.message)
    } else {
      console.log(`✅ 成就数据: 找到 ${achievements.length} 个成就`)
      achievements.forEach(ach => {
        console.log(`   - ${ach.display_name}: ${ach.description}`)
      })
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }
}

// 测试插入学习记录
async function testInsertLearningRecord() {
  console.log('\n🧪 测试插入学习记录...')
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    const testRecord = {
      user_identifier: 'test_user_' + Date.now(),
      tutorial_id: 1, // 假设ID为1的教程存在
      section_id: null,
      status: 'in_progress',
      progress_percentage: 25.5,
      total_time_spent: 15,
      active_time_spent: 12,
      scroll_percentage: 80,
      interaction_count: 5,
      pause_count: 1,
      device_type: 'web',
      learning_session_id: 'session_test_' + Date.now(),
      started_at: new Date().toISOString(),
      last_accessed_at: new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('user_learning_records')
      .insert(testRecord)
      .select()
      .single()

    if (error) {
      console.log('❌ 插入测试记录失败:', error.message)
      console.log('   错误详情:', error)
    } else {
      console.log('✅ 测试记录插入成功')
      console.log('   记录ID:', data.id)
      
      // 清理测试数据
      await supabase
        .from('user_learning_records')
        .delete()
        .eq('id', data.id)
      console.log('🧹 测试数据已清理')
    }

  } catch (error) {
    console.error('❌ 插入测试失败:', error)
  }
}

// 执行测试
async function runAllTests() {
  console.log('🚀 开始学习进度系统测试\n')
  
  await testLearningTables()
  await testInsertLearningRecord()
  
  console.log('\n✨ 测试完成')
}

runAllTests().catch(console.error)