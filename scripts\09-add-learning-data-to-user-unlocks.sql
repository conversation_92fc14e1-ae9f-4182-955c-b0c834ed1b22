-- ==========================================
-- 为 user_unlocks 表添加学习进度字段
-- 简化版学习进度追踪解决方案
-- ==========================================

-- 添加学习进度数据字段
ALTER TABLE user_unlocks 
ADD COLUMN IF NOT EXISTS learning_data JSONB DEFAULT '{}';

-- 添加学习进度更新时间字段
ALTER TABLE user_unlocks 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 创建更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_user_unlocks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
DROP TRIGGER IF EXISTS update_user_unlocks_updated_at_trigger ON user_unlocks;
CREATE TRIGGER update_user_unlocks_updated_at_trigger
    BEFORE UPDATE ON user_unlocks
    FOR EACH ROW
    EXECUTE PROCEDURE update_user_unlocks_updated_at();

-- 创建学习进度查询索引
CREATE INDEX IF NOT EXISTS idx_user_unlocks_learning_data 
ON user_unlocks USING GIN (learning_data);

CREATE INDEX IF NOT EXISTS idx_user_unlocks_updated_at 
ON user_unlocks (updated_at);

-- 查看表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_unlocks' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 测试数据插入（修正版）
DO $$
DECLARE
    test_user VARCHAR := 'test_learning_user';
    test_tutorial_id INTEGER := 1;
    existing_key_id INTEGER;
BEGIN
    -- 查找一个现有的key_id用于测试
    SELECT id INTO existing_key_id 
    FROM tutorial_keys 
    WHERE tutorial_id = test_tutorial_id 
    LIMIT 1;
    
    -- 如果没有找到现有的key，创建一个测试key
    IF existing_key_id IS NULL THEN
        INSERT INTO tutorial_keys (tutorial_id, key_code, is_used, created_at)
        VALUES (test_tutorial_id, 'TEST' || LPAD(FLOOR(RANDOM() * 100000)::TEXT, 5, '0') || 'LEARN', false, NOW())
        RETURNING id INTO existing_key_id;
        
        RAISE NOTICE '✅ 创建了测试密钥，ID: %', existing_key_id;
    END IF;
    
    -- 插入测试解锁记录（如果不存在）
    INSERT INTO user_unlocks (user_identifier, tutorial_id, key_id, unlocked_at)
    VALUES (test_user, test_tutorial_id, existing_key_id, NOW())
    ON CONFLICT (user_identifier, tutorial_id) DO NOTHING;
    
    -- 更新学习进度
    UPDATE user_unlocks 
    SET learning_data = jsonb_build_object(
        'progress_percentage', 25.5,
        'time_spent', 15,
        'status', 'in_progress',
        'completed_sections', '["section-1", "section-2"]',
        'current_section', 'section-3',
        'device_type', 'web',
        'last_updated', NOW()::text
    )
    WHERE user_identifier = test_user 
      AND tutorial_id = test_tutorial_id;
    
    RAISE NOTICE '✅ 测试学习进度数据插入成功';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ 测试数据插入失败: %', SQLERRM;
END $$;

-- 验证数据
SELECT 
    user_identifier,
    tutorial_id,
    learning_data,
    unlocked_at,
    updated_at
FROM user_unlocks 
WHERE user_identifier LIKE 'test_%'
LIMIT 5;