// 改进的学习进度跟踪策略
// 结合自动跟踪和手动标记的混合模式

export interface AdvancedLearningProgress extends LearningProgress {
  // 自动跟踪数据
  scrollProgress: number // 滚动进度 0-100
  readingSpeed: number // 阅读速度 (字符/分钟)
  timeInView: Record<string, number> // 每个章节的可视时间
  interactionEvents: number // 交互事件数量
  
  // 混合进度计算
  autoProgress: number // 基于行为的自动进度
  manualProgress: number // 基于手动标记的进度
  combinedProgress: number // 综合进度
}

/**
 * 混合进度计算策略
 */
export function calculateAdvancedProgress(
  sections: ChapterSection[],
  progressData: AdvancedLearningProgress,
  options: {
    scrollWeight: number // 滚动权重 (0.3)
    timeWeight: number // 时间权重 (0.4) 
    manualWeight: number // 手动权重 (0.3)
    minTimeThreshold: number // 最小时间阈值(秒)
  } = {
    scrollWeight: 0.3,
    timeWeight: 0.4,
    manualWeight: 0.3,
    minTimeThreshold: 30
  }
): number {
  if (sections.length === 0) return 0

  // 1. 滚动进度 (0-30分)
  const scrollScore = Math.min(progressData.scrollProgress, 100) * options.scrollWeight

  // 2. 时间进度 (0-40分)  
  const totalEstimatedTime = sections.reduce((sum, section) => 
    sum + section.estimatedTime + (section.subsections?.reduce((subSum, sub) => 
      subSum + sub.estimatedTime, 0) || 0), 0)
  
  const timeScore = Math.min(
    (progressData.totalTimeSpent / totalEstimatedTime) * 100, 
    100
  ) * options.timeWeight

  // 3. 手动标记进度 (0-30分)
  const manualScore = calculateProgressPercentage(sections, progressData.completedSections) * options.manualWeight

  // 4. 章节可视时间加权
  let timeBonus = 0
  Object.entries(progressData.timeInView).forEach(([sectionId, timeSpent]) => {
    if (timeSpent >= options.minTimeThreshold) {
      timeBonus += Math.min(timeSpent / 60, 5) // 最多5分钟加权
    }
  })

  const combinedScore = scrollScore + timeScore + manualScore + Math.min(timeBonus, 20)
  
  return Math.min(Math.round(combinedScore), 100)
}

/**
 * 智能章节完成检测
 */
export function detectSectionCompletion(
  sectionId: string,
  timeInView: number,
  scrollProgress: number,
  estimatedTime: number
): boolean {
  // 完成条件：
  // 1. 观看时间 >= 预估时间的70% 且 >= 30秒
  // 2. 滚动进度 >= 80%
  const timeThreshold = Math.max(estimatedTime * 0.7 * 60, 30) // 转换为秒
  const timeComplete = timeInView >= timeThreshold
  const scrollComplete = scrollProgress >= 80
  
  return timeComplete && scrollComplete
}

/**
 * 实时进度更新策略
 */
export class RealTimeProgressTracker {
  private updateInterval: NodeJS.Timeout | null = null
  private lastScrollPosition = 0
  private sectionTimers: Record<string, number> = {}
  private currentSection = ''
  
  constructor(
    private onProgressUpdate: (progress: AdvancedLearningProgress) => void,
    private updateFrequency: number = 5000 // 5秒更新一次
  ) {}

  start(tutorialId: number, sections: ChapterSection[]) {
    this.updateInterval = setInterval(() => {
      this.updateProgress(tutorialId, sections)
    }, this.updateFrequency)

    // 监听滚动事件
    this.setupScrollListener()
    
    // 监听可见性变化
    this.setupVisibilityListener(sections)
  }

  stop() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
  }

  private setupScrollListener() {
    let ticking = false
    const updateScrollProgress = () => {
      const scrollTop = window.pageYOffset
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollProgress = Math.min((scrollTop / docHeight) * 100, 100)
      
      this.lastScrollPosition = scrollProgress
      ticking = false
    }

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollProgress)
        ticking = true
      }
    })
  }

  private setupVisibilityListener(sections: ChapterSection[]) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const sectionId = entry.target.getAttribute('data-section') || 
                           entry.target.getAttribute('data-subsection') ||
                           entry.target.id

          if (sectionId) {
            if (entry.isIntersecting) {
              // 开始计时
              this.sectionTimers[sectionId] = Date.now()
              this.currentSection = sectionId
            } else if (this.sectionTimers[sectionId]) {
              // 停止计时，累加时间
              const timeSpent = (Date.now() - this.sectionTimers[sectionId]) / 1000
              this.addSectionTime(sectionId, timeSpent)
              delete this.sectionTimers[sectionId]
            }
          }
        })
      },
      {
        threshold: 0.5,
        rootMargin: '-50px 0px -50px 0px'
      }
    )

    // 观察所有章节
    sections.forEach(section => {
      if (section.element) observer.observe(section.element)
      section.subsections?.forEach(sub => {
        if (sub.element) observer.observe(sub.element)
      })
    })
  }

  private addSectionTime(sectionId: string, timeSpent: number) {
    // 更新章节观看时间
    const currentProgress = LearningProgressStore.load(this.getCurrentTutorialId())
    const timeInView = { ...currentProgress.timeInView }
    timeInView[sectionId] = (timeInView[sectionId] || 0) + timeSpent

    // 检查是否自动完成
    const section = this.findSection(sectionId)
    if (section && !currentProgress.completedSections.includes(sectionId)) {
      if (detectSectionCompletion(sectionId, timeInView[sectionId], this.lastScrollPosition, section.estimatedTime)) {
        currentProgress.completedSections.push(sectionId)
      }
    }

    this.onProgressUpdate({
      ...currentProgress,
      timeInView,
      scrollProgress: this.lastScrollPosition,
      currentSection: this.currentSection
    })
  }

  private getCurrentTutorialId(): number {
    // 从URL或其他方式获取当前教程ID
    return parseInt(window.location.pathname.split('/').pop() || '0')
  }

  private findSection(sectionId: string): ChapterSection | null {
    // 查找章节信息的逻辑
    return null // 需要实现
  }

  private updateProgress(tutorialId: number, sections: ChapterSection[]) {
    const currentProgress = LearningProgressStore.load(tutorialId)
    
    // 更新当前章节的观看时间
    if (this.currentSection && this.sectionTimers[this.currentSection]) {
      const timeSpent = (Date.now() - this.sectionTimers[this.currentSection]) / 1000
      this.addSectionTime(this.currentSection, timeSpent)
      this.sectionTimers[this.currentSection] = Date.now() // 重置计时器
    }

    // 计算综合进度
    const advancedProgress: AdvancedLearningProgress = {
      ...currentProgress,
      scrollProgress: this.lastScrollPosition,
      readingSpeed: this.calculateReadingSpeed(currentProgress.totalTimeSpent),
      timeInView: currentProgress.timeInView || {},
      interactionEvents: 0, // 需要实现
      autoProgress: 0, // 基于行为计算
      manualProgress: calculateProgressPercentage(sections, currentProgress.completedSections),
      combinedProgress: calculateAdvancedProgress(sections, currentProgress as AdvancedLearningProgress)
    }

    this.onProgressUpdate(advancedProgress)
  }

  private calculateReadingSpeed(totalTime: number): number {
    // 基于总时间和内容长度计算阅读速度
    return 200 // 默认200字符/分钟
  }
}