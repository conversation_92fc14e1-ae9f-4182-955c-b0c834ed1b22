"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  BookOpen, 
  Clock, 
  CheckCircle, 
  Circle, 
  Play, 
  Target,
  Trophy,
  MapPin,
  ChevronRight,
  ChevronDown
} from 'lucide-react'
import { 
  scrollToSection, 
  formatLearningTime,
  type ChapterSection 
} from '@/lib/learning-utils'

interface TableOfContentsProps {
  sections?: ChapterSection[]
  chapters?: ChapterSection[]  // 兼容教程页面的调用
  currentSectionId?: string
  currentSection?: string     // 兼容教程页面的调用
  completedSections?: string[]
  totalProgress?: number      // 兼容教程页面的调用
  totalTimeSpent?: number     // 兼容教程页面的调用
  onSectionClick?: (sectionId: string) => void
  onMarkComplete?: (sectionId: string) => void  // 兼容教程页面的调用
  className?: string
}

export function TableOfContents({
  sections: propSections,
  chapters,
  currentSectionId: propCurrentSectionId = '',
  currentSection,
  completedSections = [],
  totalProgress,
  totalTimeSpent,
  onSectionClick,
  onMarkComplete,
  className = ''
}: TableOfContentsProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())

  // 兼容性处理：支持 sections 或 chapters 参数
  const sections = propSections || chapters || []
  const currentSectionId = propCurrentSectionId || currentSection || ''

  // 计算总体进度
  const totalSections = sections.reduce((count, section) => {
    return count + 1 + (section.subsections?.length || 0)
  }, 0)

  const completedCount = sections.reduce((count, section) => {
    let sectionCount = completedSections.includes(section.id) ? 1 : 0
    if (section.subsections) {
      sectionCount += section.subsections.filter(sub => 
        completedSections.includes(sub.id)
      ).length
    }
    return count + sectionCount
  }, 0)

  const progressPercentage = totalProgress !== undefined ? totalProgress : 
    (totalSections > 0 ? Math.round((completedCount / totalSections) * 100) : 0)

  // 计算总学习时间
  const totalTime = totalTimeSpent || sections.reduce((time, section) => {
    let sectionTime = section.estimatedTime
    if (section.subsections) {
      sectionTime += section.subsections.reduce((subTime, sub) => subTime + sub.estimatedTime, 0)
    }
    return time + sectionTime
  }, 0)

  // 自动展开包含当前章节的章节
  useEffect(() => {
    if (currentSectionId) {
      const parentSection = sections.find(section => 
        section.id === currentSectionId || 
        section.subsections?.some(sub => sub.id === currentSectionId)
      )
      if (parentSection && parentSection.subsections) {
        setExpandedSections(prev => new Set(prev).add(parentSection.id))
      }
    }
  }, [currentSectionId, sections])

  const handleSectionClick = (sectionId: string) => {
    if (onSectionClick) {
      onSectionClick(sectionId)
    } else {
      scrollToSection(sectionId)
    }
  }

  const toggleSectionExpansion = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const getSectionIcon = (section: ChapterSection) => {
    if (completedSections.includes(section.id)) {
      return <CheckCircle className="h-4 w-4 text-green-600" />
    }
    
    switch (section.type) {
      case 'intro':
        return <Play className="h-4 w-4 text-blue-600" />
      case 'checkpoint':
        return <Target className="h-4 w-4 text-orange-600" />
      case 'interactive':
        return <Trophy className="h-4 w-4 text-purple-600" />
      case 'conclusion':
        return <Trophy className="h-4 w-4 text-green-600" />
      default:
        return <Circle className="h-4 w-4 text-gray-400" />
    }
  }

  const getSectionTypeLabel = (type: ChapterSection['type']) => {
    switch (type) {
      case 'intro': return '介绍'
      case 'checkpoint': return '检查点'
      case 'interactive': return '练习'
      case 'conclusion': return '总结'
      default: return '章节'
    }
  }

  if (sections.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <BookOpen className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500">暂无目录结构</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`${className} toc-container sticky top-4 flex flex-col overflow-hidden`}>
      <CardHeader className="pb-3 flex-shrink-0 px-3">
        <CardTitle className="flex items-center text-base">
          <BookOpen className="h-4 w-4 mr-2 flex-shrink-0" />
          <span className="truncate">教程目录</span>
        </CardTitle>
        <CardDescription className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="truncate">学习进度</span>
            <span className="font-medium text-xs whitespace-nowrap">{completedCount}/{totalSections}</span>
          </div>
          <Progress value={progressPercentage} className="w-full h-2" />
          <div className="flex items-center justify-between text-xs text-gray-600">
            <div className="flex items-center min-w-0 flex-1">
              <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="truncate">预计 {formatLearningTime(totalTime)}</span>
            </div>
            <span className="font-medium ml-2 whitespace-nowrap">{progressPercentage}%</span>
          </div>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="p-0 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        <div className="space-y-1 px-2 pb-2">
          {sections.map((section, index) => (
            <div key={section.id}>
              {/* 主章节 */}
              <div
                className={`group relative flex items-center p-2 hover:bg-gray-50 cursor-pointer transition-colors rounded-md ${
                  currentSectionId === section.id ? 'bg-blue-50 border-l-2 border-blue-500' : ''
                }`}
                onClick={() => handleSectionClick(section.id)}
              >
                {/* 展开/收起按钮 */}
                {section.subsections && section.subsections.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 mr-1 hover:bg-transparent flex-shrink-0"
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleSectionExpansion(section.id)
                    }}
                  >
                    {expandedSections.has(section.id) ? (
                      <ChevronDown className="h-3 w-3" />
                    ) : (
                      <ChevronRight className="h-3 w-3" />
                    )}
                  </Button>
                )}
                
                {/* 图标 */}
                <div className="flex items-center mr-2 flex-shrink-0">
                  {getSectionIcon(section)}
                </div>
                
                {/* 内容区域 */}
                <div className="flex-1 min-w-0 overflow-hidden">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-1 mb-1">
                      <span className="font-medium text-sm text-gray-900 truncate block toc-text-safe">
                        {section.title}
                      </span>
                      {currentSectionId === section.id && (
                        <MapPin className="h-3 w-3 text-blue-600 flex-shrink-0" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
                        <span className="whitespace-nowrap">{section.estimatedTime}分钟</span>
                      </div>
                      <Badge variant="outline" className="toc-badge">
                        {getSectionTypeLabel(section.type)}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* 子章节 */}
              {section.subsections && expandedSections.has(section.id) && (
                <div className="ml-4 border-l border-gray-200 pl-2 space-y-1">
                  {section.subsections.map((subsection) => (
                    <div
                      key={subsection.id}
                      className={`group relative flex items-center p-2 hover:bg-gray-50 cursor-pointer transition-colors rounded-md ${
                        currentSectionId === subsection.id ? 'bg-blue-50 border-l-2 border-blue-500' : ''
                      }`}
                      onClick={() => handleSectionClick(subsection.id)}
                    >
                      {/* 图标 */}
                      <div className="flex items-center mr-2 flex-shrink-0">
                        {getSectionIcon(subsection)}
                      </div>
                      
                      {/* 内容区域 */}
                      <div className="flex-1 min-w-0 overflow-hidden">
                        <div className="flex flex-col">
                          <div className="flex items-center gap-1 mb-1">
                            <span className="text-sm text-gray-800 truncate block toc-text-safe">
                              {subsection.title}
                            </span>
                            {currentSectionId === subsection.id && (
                              <MapPin className="h-3 w-3 text-blue-600 flex-shrink-0" />
                            )}
                          </div>
                          <div className="flex items-center text-xs text-gray-500">
                            <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
                            <span className="whitespace-nowrap">{subsection.estimatedTime}分钟</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* 分隔线 */}
              {index < sections.length - 1 && (
                <Separator className="my-1" />
              )}
            </div>
          ))}
        </div>
      </CardContent>
        
      {/* 底部统计信息 - 固定在底部 */}
      <div className="p-3 bg-gray-50 border-t flex-shrink-0">
        <div className="grid grid-cols-3 gap-2 text-xs text-gray-600">
          <div className="flex flex-col items-center">
            <BookOpen className="h-3 w-3 mb-1" />
            <span className="truncate">{sections.length}章节</span>
          </div>
          <div className="flex flex-col items-center">
            <Target className="h-3 w-3 mb-1" />
            <span className="truncate">{sections.filter(s => s.type === 'checkpoint').length}检查</span>
          </div>
          <div className="flex flex-col items-center">
            <Trophy className="h-3 w-3 mb-1" />
            <span className="truncate">{sections.filter(s => s.type === 'interactive').length}练习</span>
          </div>
        </div>
      </div>
    </Card>
  )
}