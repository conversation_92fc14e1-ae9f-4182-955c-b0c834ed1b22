/**
 * 验证简化滚动跟踪系统实际效果
 * 在浏览器控制台中运行此脚本
 */

const verifyScrollTracking = () => {
  console.log('🔍 验证简化滚动跟踪系统...\n')
  
  // 1. 检查跟踪器是否已启动
  console.log('1. 检查跟踪器状态:')
  const trackerIndicator = document.querySelector('[data-testid="learning-tracker-status"]') || 
                          document.querySelector('span:contains("实时跟踪中")')
  
  if (trackerIndicator) {
    console.log('  ✅ 跟踪器状态指示器存在')
  } else {
    console.log('  ⚠️ 未找到跟踪器状态指示器')
  }
  
  // 2. 测试滚动进度更新
  console.log('\n2. 测试滚动进度更新:')
  const progressElement = document.querySelector('[data-testid="progress-percentage"]') ||
                         document.querySelector('span:contains("%")')
  
  if (progressElement) {
    const initialProgress = progressElement.textContent
    console.log(`  当前进度: ${initialProgress}`)
    
    // 模拟滚动测试
    console.log('  开始滚动测试...')
    
    // 滚动到页面25%位置
    const quarter = document.documentElement.scrollHeight * 0.25
    window.scrollTo({ top: quarter, behavior: 'smooth' })
    
    setTimeout(() => {
      const newProgress = progressElement.textContent
      console.log(`  滚动到25%位置后进度: ${newProgress}`)
      
      // 滚动到页面50%位置
      const half = document.documentElement.scrollHeight * 0.5
      window.scrollTo({ top: half, behavior: 'smooth' })
      
      setTimeout(() => {
        const halfProgress = progressElement.textContent
        console.log(`  滚动到50%位置后进度: ${halfProgress}`)
        
        // 往回滚动测试
        window.scrollTo({ top: quarter, behavior: 'smooth' })
        
        setTimeout(() => {
          const backProgress = progressElement.textContent
          console.log(`  往回滚动后进度: ${backProgress}`)
          
          if (backProgress === halfProgress) {
            console.log('  ✅ 往回滚动进度不减少 - 测试通过')
          } else {
            console.log('  ❌ 往回滚动进度发生变化 - 需要调试')
          }
        }, 1000)
      }, 1000)
    }, 1000)
  } else {
    console.log('  ⚠️ 未找到进度显示元素')
  }
  
  // 3. 检查目录高亮功能
  console.log('\n3. 检查目录高亮功能:')
  const tocElement = document.querySelector('[data-testid="table-of-contents"]') ||
                    document.querySelector('.table-of-contents') ||
                    document.querySelector('nav')
  
  if (tocElement) {
    console.log('  ✅ 找到目录组件')
    
    // 查找高亮的章节
    const highlightedSections = tocElement.querySelectorAll('.bg-blue-50, .border-blue-500, [class*="active"]')
    console.log(`  当前高亮章节数: ${highlightedSections.length}`)
    
    if (highlightedSections.length > 0) {
      highlightedSections.forEach((section, index) => {
        const sectionText = section.textContent?.trim()
        console.log(`  高亮章节 ${index + 1}: ${sectionText}`)
      })
    }
  } else {
    console.log('  ⚠️ 未找到目录组件')
  }
  
  // 4. 检查控制台日志
  console.log('\n4. 监听跟踪器日志:')
  console.log('  请观察控制台是否出现以下日志:')
  console.log('    - "📊 滚动进度更新: XX%"')
  console.log('    - "📍 当前章节: xxx"')
  console.log('    - "🎯 简化滚动跟踪已启动"')
  
  // 5. 性能测试
  console.log('\n5. 性能测试:')
  const startTime = performance.now()
  
  // 快速滚动测试
  let scrollCount = 0
  const scrollTest = setInterval(() => {
    window.scrollBy(0, 50)
    scrollCount++
    
    if (scrollCount >= 10) {
      clearInterval(scrollTest)
      const endTime = performance.now()
      const duration = endTime - startTime
      console.log(`  快速滚动 ${scrollCount} 次耗时: ${duration.toFixed(2)}ms`)
      
      if (duration < 1000) {
        console.log('  ✅ 滚动性能良好')
      } else {
        console.log('  ⚠️ 滚动性能可能需要优化')
      }
    }
  }, 100)
  
  // 6. 本地存储检查
  console.log('\n6. 本地存储检查:')
  const storageKeys = Object.keys(localStorage).filter(key => key.includes('learning_progress'))
  console.log(`  学习进度存储键数量: ${storageKeys.length}`)
  
  storageKeys.forEach(key => {
    const data = JSON.parse(localStorage.getItem(key) || '{}')
    console.log(`  ${key}: 进度${data.progressPercentage || 0}%`)
  })
  
  console.log('\n🎯 验证完成！请观察以上结果和控制台日志。')
  console.log('\n📝 预期效果:')
  console.log('  • 滚动时进度立即更新')
  console.log('  • 目录自动高亮当前章节')
  console.log('  • 往回滚动时进度不减少')
  console.log('  • 状态指示器显示"📜 实时跟踪中"')
  console.log('  • 章节切换时显示toast提示')
}

// 运行验证
verifyScrollTracking()

// 提供手动测试函数
window.testScrollProgress = () => {
  console.log('手动滚动测试开始...')
  const positions = [0.2, 0.4, 0.6, 0.8, 1.0]
  let index = 0
  
  const scrollNext = () => {
    if (index < positions.length) {
      const position = positions[index]
      const scrollTop = document.documentElement.scrollHeight * position
      window.scrollTo({ top: scrollTop, behavior: 'smooth' })
      console.log(`滚动到 ${(position * 100).toFixed(0)}% 位置`)
      index++
      setTimeout(scrollNext, 2000)
    } else {
      console.log('手动测试完成！')
    }
  }
  
  scrollNext()
}

console.log('\n💡 提示: 运行 testScrollProgress() 进行自动滚动测试')