#!/usr/bin/env node

/**
 * 直接测试学习进度数据操作 - 模拟API功能
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function testLearningDataOperations() {
  console.log('🧪 测试学习进度数据操作（模拟API）...')
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    // 查找现有教程
    const { data: existingTutorial, error: tutorialError } = await supabase
      .from('tutorials')
      .select('id')
      .limit(1)
      .single()

    if (tutorialError || !existingTutorial) {
      console.log('❌ 没有找到现有教程，无法测试')
      return
    }

    const testTutorialId = existingTutorial.id
    const testUser = 'test_learning_user_' + Date.now()
    console.log('📖 使用教程ID:', testTutorialId)
    console.log('👤 测试用户:', testUser)

    // 1. 模拟学习进度数据
    const learningMetadata = {
      progress_percentage: 45.5,
      time_spent: 25,
      status: 'in_progress',
      completed_sections: ['section-1', 'section-2', 'section-3'],
      current_section: 'section-4',
      device_type: 'web',
      last_updated: new Date().toISOString(),
      scroll_percentage: 75,
      interaction_count: 10
    }

    console.log('📊 学习进度数据:', JSON.stringify(learningMetadata, null, 2))

    // 2. 创建测试解锁记录
    // 首先查找可用的密钥
    const { data: existingKey, error: keyError } = await supabase
      .from('tutorial_keys')
      .select('id')
      .eq('tutorial_id', testTutorialId)
      .limit(1)
      .single()

    let keyId = existingKey?.id

    if (!keyId) {
      // 创建测试密钥
      const testKeyCode = 'TEST' + Math.random().toString(36).substr(2, 20).toUpperCase()
      
      const { data: newKey, error: createKeyError } = await supabase
        .from('tutorial_keys')
        .insert({
          tutorial_id: testTutorialId,
          key_code: testKeyCode,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (createKeyError) {
        console.log('❌ 创建测试密钥失败:', createKeyError.message)
        return
      }

      keyId = newKey.id
      console.log('✅ 测试密钥创建成功，ID:', keyId)
    }

    // 创建解锁记录
    const { data: unlockData, error: unlockError } = await supabase
      .from('user_unlocks')
      .insert({
        user_identifier: testUser,
        tutorial_id: testTutorialId,
        key_id: keyId,
        unlocked_at: new Date().toISOString()
      })
      .select()
      .single()

    if (unlockError) {
      console.log('❌ 创建测试解锁记录失败:', unlockError.message)
      return
    }

    console.log('✅ 测试解锁记录创建成功，ID:', unlockData.id)

    // 3. 测试学习进度更新（模拟API POST操作）
    console.log('\n🔄 测试学习进度更新...')
    
    const { data: updatedRecord, error: updateError } = await supabase
      .from('user_unlocks')
      .update({
        learning_data: learningMetadata,
        updated_at: new Date().toISOString()
      })
      .eq('user_identifier', testUser)
      .eq('tutorial_id', testTutorialId)
      .select()
      .single()

    if (updateError) {
      console.log('❌ 学习进度更新失败:', updateError.message)
      console.log('错误详情:', updateError)
      return
    }

    console.log('✅ 学习进度更新成功!')
    console.log('📈 更新后的记录:', JSON.stringify(updatedRecord.learning_data, null, 2))

    // 4. 测试学习进度查询（模拟API GET操作）
    console.log('\n📊 测试学习进度查询...')
    
    const { data: queryResult, error: queryError } = await supabase
      .from('user_unlocks')
      .select(`
        *,
        tutorials (
          id, title, description, category_id
        )
      `)
      .eq('user_identifier', testUser)

    if (queryError) {
      console.log('❌ 学习进度查询失败:', queryError.message)
      return
    }

    console.log('✅ 学习进度查询成功!')
    
    if (queryResult && queryResult.length > 0) {
      const record = queryResult[0]
      console.log('📚 教程信息:', record.tutorials?.title || '未知教程')
      console.log('📊 学习进度:', record.learning_data?.progress_percentage + '%')
      console.log('⏱️ 学习时长:', record.learning_data?.time_spent + '分钟')
      console.log('📍 当前状态:', record.learning_data?.status)
      console.log('📑 完成章节:', record.learning_data?.completed_sections?.length + '个')
    }

    // 5. 测试成就逻辑（简化版）
    console.log('\n🏆 测试成就检查...')
    const achievements = []
    
    if (learningMetadata.progress_percentage === 100) {
      achievements.push({
        id: 'completion',
        display_name: '完成学习',
        description: '恭喜完成本教程学习！',
        points: 50
      })
    } else if (learningMetadata.progress_percentage >= 50) {
      achievements.push({
        id: 'halfway',
        display_name: '过半达成',
        description: '学习进度已过半，继续加油！',
        points: 25
      })
    }

    if (achievements.length > 0) {
      console.log('🎉 获得成就:', achievements.map(a => a.display_name).join(', '))
    } else {
      console.log('💪 继续努力，更多成就等着你！')
    }

    // 6. 清理测试数据
    console.log('\n🧹 清理测试数据...')
    await supabase
      .from('user_unlocks')
      .delete()
      .eq('id', unlockData.id)
    
    console.log('✅ 测试数据已清理')

    // 7. 最终验证
    console.log('\n✨ 测试结果总结:')
    console.log('  ✅ 数据库learning_data字段可正常写入和读取')
    console.log('  ✅ 学习进度数据格式正确')
    console.log('  ✅ 解锁记录关联正常')
    console.log('  ✅ 成就检查逻辑正常')
    console.log('  ✅ 数据清理正常')
    console.log('\n🎯 学习进度系统(简化版)功能正常！')

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }
}

// 执行测试
testLearningDataOperations().catch(console.error)