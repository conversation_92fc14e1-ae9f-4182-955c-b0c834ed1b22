/* 自定义滚动条样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Webkit 滚动条样式 (Chrome, Safari, Edge) */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: #d1d5db;
  }

  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background: #f3f4f6;
  }

  /* Firefox 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
  }

  /* 学习进度相关样式 */
  .progress-indicator {
    position: relative;
  }

  .progress-indicator::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .progress-indicator.active::before {
    opacity: 1;
  }

  /* 目录项悬停效果 */
  .toc-item {
    transition: all 0.2s ease;
  }

  .toc-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* 当前章节高亮 */
  .current-section {
    background: linear-gradient(to right, #eff6ff, #dbeafe);
    border-right: 3px solid #3b82f6;
  }

  /* 完成状态动画 */
  .completion-animation {
    animation: completion-pulse 0.6s ease-in-out;
  }

  @keyframes completion-pulse {
    0% {
      transform: scale(1);
      background-color: transparent;
    }
    50% {
      transform: scale(1.02);
      background-color: #dcfce7;
    }
    100% {
      transform: scale(1);
      background-color: transparent;
    }
  }

  /* 进度条平滑动画 */
  .progress-smooth {
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 固定目录容器 */
  .sticky-toc {
    position: sticky;
    top: 1rem;
    max-height: calc(100vh - 2rem);
    overflow: hidden;
  }

  /* 响应式目录 */
  @media (max-width: 1024px) {
    .sticky-toc {
      position: relative;
      top: 0;
      max-height: none;
      margin-bottom: 1rem;
    }
  }
}