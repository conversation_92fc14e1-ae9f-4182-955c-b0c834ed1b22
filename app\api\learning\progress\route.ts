import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { getUserIdentifier } from "@/lib/auth"

// ==========================================
// 学习进度跟踪API - 临时简化版本
// 使用现有表结构，避免创建新表
// ==========================================

interface LearningProgressRequest {
  tutorialId: number
  sectionId?: number
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped' | 'paused'
  progressPercentage: number
  timeSpent: number
  interactionData?: {
    scrollPercentage?: number
    interactionCount?: number
    pauseCount?: number
    deviceType?: string
    completedSections?: string[]
    currentSection?: string
  }
}

interface APIResponse<T> {
  success: boolean
  data?: T
  error?: string
  achievements?: any[]
}

/**
 * 记录学习进度 - 简化版本
 */
export async function POST(request: NextRequest) {
  try {
    console.log('📚 开始记录学习进度（简化版）')
    
    const userIdentifier = getUserIdentifier(request)
    const progressData: LearningProgressRequest = await request.json()
    
    console.log('👤 用户:', userIdentifier)
    console.log('📊 进度数据:', progressData)

    // 使用现有的 user_unlocks 表来存储简化的学习进度
    // 添加一个额外的 learning_data 字段来存储详细进度
    const learningMetadata = {
      progress_percentage: progressData.progressPercentage,
      time_spent: progressData.timeSpent,
      status: progressData.status,
      completed_sections: progressData.interactionData?.completedSections || [],
      current_section: progressData.interactionData?.currentSection || '',
      device_type: progressData.interactionData?.deviceType || 'web',
      last_updated: new Date().toISOString(),
      scroll_percentage: progressData.interactionData?.scrollPercentage || 0,
      interaction_count: progressData.interactionData?.interactionCount || 0
    }

    // 检查用户是否已解锁此教程
    const { data: existingUnlock, error: checkError } = await supabaseAdmin
      .from('user_unlocks')
      .select('*')
      .eq('user_identifier', userIdentifier)
      .eq('tutorial_id', progressData.tutorialId)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ 检查用户解锁状态失败:', checkError)
      return NextResponse.json({
        success: false,
        error: `查询错误: ${checkError.message}`
      }, { status: 500 })
    }

    if (!existingUnlock) {
      return NextResponse.json({
        success: false,
        error: '用户未解锁此教程'
      }, { status: 403 })
    }

    // 更新学习进度数据到 user_unlocks 表
    const { data: updatedRecord, error: updateError } = await supabaseAdmin
      .from('user_unlocks')
      .update({
        learning_data: learningMetadata,
        updated_at: new Date().toISOString()
      })
      .eq('user_identifier', userIdentifier)
      .eq('tutorial_id', progressData.tutorialId)
      .select()
      .single()

    if (updateError) {
      console.error('❌ 学习进度更新失败:', updateError)
      return NextResponse.json({
        success: false,
        error: `更新失败: ${updateError.message}`,
        details: updateError
      }, { status: 500 })
    }

    console.log('✅ 学习进度保存成功')

    // 模拟成就检查（简化版）
    const achievements = []
    if (progressData.progressPercentage === 100) {
      achievements.push({
        id: 'completion',
        display_name: '完成学习',
        description: '恭喜完成本教程学习！',
        points: 50
      })
    } else if (progressData.progressPercentage >= 50 && progressData.progressPercentage < 100) {
      achievements.push({
        id: 'halfway',
        display_name: '过半达成',
        description: '学习进度已过半，继续加油！',
        points: 25
      })
    }

    const response: APIResponse<typeof updatedRecord> = {
      success: true,
      data: updatedRecord,
      achievements: achievements
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ 学习进度记录异常:', error)
    
    // 详细的错误信息
    let errorMessage = '学习进度记录失败'
    let errorDetails = null
    
    if (error instanceof Error) {
      errorMessage = error.message
      errorDetails = {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 3)
      }
    } else if (typeof error === 'object' && error !== null) {
      errorMessage = JSON.stringify(error)
      errorDetails = error
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      details: errorDetails,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 获取学习统计 - 简化版本
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userIdentifier = searchParams.get('user') || getUserIdentifier(request)
    const tutorialId = searchParams.get('tutorialId')

    console.log('📊 查询学习统计（简化版）:', { userIdentifier, tutorialId })

    // 从 user_unlocks 表获取学习数据
    let query = supabaseAdmin
      .from('user_unlocks')
      .select(`
        *,
        tutorials (
          id, title, description, category_id
        )
      `)
      .eq('user_identifier', userIdentifier)

    if (tutorialId) {
      query = query.eq('tutorial_id', parseInt(tutorialId))
    }

    const { data: unlockData, error: unlockError } = await query

    if (unlockError) {
      console.error('❌ 学习数据查询失败:', unlockError)
      throw unlockError
    }

    // 计算总体统计
    const totalTutorials = unlockData.length
    const completedTutorials = unlockData.filter(unlock => 
      unlock.learning_data?.status === 'completed'
    ).length
    const totalLearningTime = unlockData.reduce((total, unlock) => 
      total + (unlock.learning_data?.time_spent || 0), 0
    )

    const userStats = {
      user_identifier: userIdentifier,
      total_tutorials_unlocked: totalTutorials,
      total_tutorials_started: unlockData.filter(unlock => 
        unlock.learning_data?.status && unlock.learning_data.status !== 'not_started'
      ).length,
      total_tutorials_completed: completedTutorials,
      total_learning_time: totalLearningTime,
      completion_rate: totalTutorials > 0 ? Math.round((completedTutorials / totalTutorials) * 100) : 0
    }

    const response = {
      success: true,
      data: {
        userStats,
        tutorialProgress: unlockData,
        recentAchievements: [] // 简化版暂不支持
      }
    }

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'private, max-age=30'
      }
    })

  } catch (error) {
    console.error('❌ 学习统计查询异常:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '学习统计查询失败'
    }, { status: 500 })
  }
}